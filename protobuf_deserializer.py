#!/usr/bin/env python3
"""
Protocol Buffers 反序列化器 - 用于解析座位数据

基于 fetchAvailSeatData2 的实现逻辑，使用Python重现JavaScript的Protocol Buffers反序列化过程。

使用方法:
    python protobuf_deserializer.py <binary_data_file>
    
或者在代码中调用:
    deserializer = ProtobufDeserializer()
    result = deserializer.deserialize_show_area(binary_data)
"""

import struct
import sys
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass


@dataclass
class ShowSeatData:
    """座位数据结构"""
    id: str = ""                    # k字段 - 座位ID
    name: str = ""                  # b字段 - 座位名称
    row_name: str = ""              # i字段 - 排名
    geometry: str = ""              # g字段 - 坐标字符串
    color: str = ""                 # e字段 - 颜色
    fare_level: str = ""            # ai字段 - 票价等级
    x: Optional[str] = None         # x字段 - X坐标
    y: Optional[str] = None         # y字段 - Y坐标
    field_a: int = 0                # a字段 - 数值字段
    field_n: int = 0                # n字段 - 数值字段
    field_s: str = ""               # s字段 - 可选字段


@dataclass
class ShowAreaData:
    """区域数据结构"""
    area_id: str = ""               # k字段 - 区域ID
    area_name: str = ""             # a字段 - 区域名称
    seats: List[ShowSeatData] = None  # ai字段 - 座位数组
    field_n: str = ""               # n字段 - 可选字段
    field_g: str = ""               # g字段 - 可选字段
    field_e: int = 0                # e字段 - 数值字段
    field_b: int = 0                # b字段 - 数值字段
    field_i: str = ""               # i字段 - 可选字段
    field_s: str = ""               # s字段 - 可选字段

    def __post_init__(self):
        if self.seats is None:
            self.seats = []


class ProtobufDeserializer:
    """Protocol Buffers 反序列化器"""
    
    def __init__(self):
        self.data = b''
        self.position = 0
    
    def _read_varint(self) -> int:
        """读取变长整数 (varint)"""
        result = 0
        shift = 0
        
        while self.position < len(self.data):
            byte = self.data[self.position]
            self.position += 1
            
            result |= (byte & 0x7F) << shift
            
            if (byte & 0x80) == 0:
                return result
            
            shift += 7
            if shift >= 32:
                raise ValueError("Varint too long")
        
        raise ValueError("Unexpected end of data while reading varint")
    
    def _read_length_delimited(self) -> bytes:
        """读取长度分隔的数据"""
        length = self._read_varint()
        if self.position + length > len(self.data):
            raise ValueError("Not enough data for length-delimited field")
        
        result = self.data[self.position:self.position + length]
        self.position += length
        return result
    
    def _read_string(self) -> str:
        """读取字符串"""
        bytes_data = self._read_length_delimited()
        return bytes_data.decode('utf-8')
    
    def _read_uint64(self) -> int:
        """读取64位无符号整数 (作为varint)"""
        return self._read_varint()
    
    def _read_tag(self) -> tuple:
        """读取字段标签，返回 (field_number, wire_type)"""
        tag = self._read_varint()
        field_number = tag >> 3
        wire_type = tag & 0x7
        return field_number, wire_type
    
    def _skip_field(self, wire_type: int):
        """跳过未知字段"""
        if wire_type == 0:  # varint
            self._read_varint()
        elif wire_type == 1:  # 64-bit
            self.position += 8
        elif wire_type == 2:  # length-delimited
            self._read_length_delimited()
        elif wire_type == 5:  # 32-bit
            self.position += 4
        else:
            raise ValueError(f"Unknown wire type: {wire_type}")
    
    def _deserialize_show_seat(self, data: bytes) -> ShowSeatData:
        """反序列化 ShowSeatPb 消息"""
        old_data = self.data
        old_position = self.position
        
        self.data = data
        self.position = 0
        
        seat = ShowSeatData()
        
        try:
            while self.position < len(self.data):
                field_number, wire_type = self._read_tag()
                
                if field_number == 1:  # k - 座位ID
                    if wire_type == 2:  # string
                        seat.id = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 2:  # a - 数值字段
                    if wire_type == 0:  # varint
                        seat.field_a = self._read_uint64()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 3:  # n - 数值字段
                    if wire_type == 0:  # varint
                        seat.field_n = self._read_uint64()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 4:  # g - 几何坐标
                    if wire_type == 2:  # string
                        seat.geometry = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 5:  # e - 颜色
                    if wire_type == 2:  # string
                        seat.color = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 6:  # b - 座位名称
                    if wire_type == 2:  # string
                        seat.name = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 7:  # i - 排名
                    if wire_type == 2:  # string
                        seat.row_name = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 8:  # s - 可选字段
                    if wire_type == 2:  # string
                        seat.field_s = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 9:  # ai - 票价等级
                    if wire_type == 2:  # string
                        seat.fare_level = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 10:  # x - X坐标
                    if wire_type == 2:  # string
                        seat.x = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                elif field_number == 11:  # y - Y坐标
                    if wire_type == 2:  # string
                        seat.y = self._read_string()
                    else:
                        self._skip_field(wire_type)
                
                else:
                    # 跳过未知字段
                    self._skip_field(wire_type)
        
        finally:
            self.data = old_data
            self.position = old_position
        
        return seat
    
    def deserialize_show_area(self, binary_data: bytes) -> ShowAreaData:
        """反序列化 ShowAreaPb 消息"""
        self.data = binary_data
        self.position = 0
        
        area = ShowAreaData()
        
        while self.position < len(self.data):
            field_number, wire_type = self._read_tag()
            
            if field_number == 1:  # k - 区域ID
                if wire_type == 2:  # string
                    area.area_id = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 2:  # a - 区域名称
                if wire_type == 2:  # string
                    area.area_name = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 3:  # n - 可选字段
                if wire_type == 2:  # string
                    area.field_n = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 4:  # g - 可选字段
                if wire_type == 2:  # string
                    area.field_g = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 5:  # e - 数值字段
                if wire_type == 0:  # varint
                    area.field_e = self._read_uint64()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 6:  # b - 数值字段
                if wire_type == 0:  # varint
                    area.field_b = self._read_uint64()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 7:  # i - 可选字段
                if wire_type == 2:  # string
                    area.field_i = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 8:  # s - 可选字段
                if wire_type == 2:  # string
                    area.field_s = self._read_string()
                else:
                    self._skip_field(wire_type)
            
            elif field_number == 9:  # ai - 座位数组
                if wire_type == 2:  # length-delimited (嵌套消息)
                    seat_data = self._read_length_delimited()
                    seat = self._deserialize_show_seat(seat_data)
                    area.seats.append(seat)
                else:
                    self._skip_field(wire_type)
            
            else:
                # 跳过未知字段
                self._skip_field(wire_type)
        
        return area
    
    def to_javascript_format(self, area_data: ShowAreaData, area_id: str) -> List[Dict[str, Any]]:
        """转换为JavaScript格式的座位数据数组"""
        result = []
        
        for seat in area_data.seats:
            # 解析坐标字符串
            coords = []
            if seat.geometry:
                try:
                    coord_parts = seat.geometry.split(',')
                    coords = [float(coord.strip()) for coord in coord_parts[:2]]
                except (ValueError, IndexError):
                    coords = [0, 0]
            
            # 转换X, Y坐标
            x_coord = None
            y_coord = None
            if seat.x:
                try:
                    x_coord = float(seat.x)
                except ValueError:
                    pass
            if seat.y:
                try:
                    y_coord = float(seat.y)
                except ValueError:
                    pass
            
            seat_dict = {
                'coords': coords,
                'coordsStr': seat.geometry,
                'aid': area_id,
                'color': seat.color,
                'fareLevel': seat.fare_level,
                'id': seat.id,
                'rowName': seat.row_name,
                'name': seat.name,
                'areaName': area_data.area_name,
                'x': x_coord,
                'y': y_coord,
            }
            
            result.append(seat_dict)
        
        return result


def main():
    """命令行入口"""
    if len(sys.argv) != 2:
        print("Usage: python protobuf_deserializer.py <binary_data_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        with open(filename, 'rb') as f:
            binary_data = f.read()
        
        deserializer = ProtobufDeserializer()
        area_data = deserializer.deserialize_show_area(binary_data)
        
        print(f"区域名称: {area_data.area_name}")
        print(f"区域ID: {area_data.area_id}")
        print(f"座位数量: {len(area_data.seats)}")
        
        # 转换为JavaScript格式
        js_format = deserializer.to_javascript_format(area_data, area_data.area_id)
        
        print("\n前5个座位数据:")
        for i, seat in enumerate(js_format[:5]):
            print(f"座位 {i+1}: {seat}")
        
        return js_format
        
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


# 使用示例和测试代码
def create_test_data() -> bytes:
    """创建测试用的Protocol Buffers数据"""
    # 这是一个简化的测试数据生成器
    # 实际使用时，你需要从API获取真实的二进制数据

    def encode_varint(value: int) -> bytes:
        """编码varint"""
        result = b''
        while value >= 0x80:
            result += bytes([value & 0x7F | 0x80])
            value >>= 7
        result += bytes([value & 0x7F])
        return result

    def encode_string(field_num: int, value: str) -> bytes:
        """编码字符串字段"""
        tag = (field_num << 3) | 2  # wire_type = 2 for length-delimited
        data = value.encode('utf-8')
        return encode_varint(tag) + encode_varint(len(data)) + data

    def encode_uint64(field_num: int, value: int) -> bytes:
        """编码uint64字段"""
        tag = (field_num << 3) | 0  # wire_type = 0 for varint
        return encode_varint(tag) + encode_varint(value)

    # 创建一个测试座位
    seat_data = b''
    seat_data += encode_string(1, "SEAT_001")  # k - 座位ID
    seat_data += encode_uint64(2, 100)         # a - 数值字段
    seat_data += encode_uint64(3, 200)         # n - 数值字段
    seat_data += encode_string(4, "10,20")     # g - 坐标
    seat_data += encode_string(5, "#FF0000")   # e - 颜色
    seat_data += encode_string(6, "A1")        # b - 座位名称
    seat_data += encode_string(7, "第1排")      # i - 排名
    seat_data += encode_string(9, "VIP")       # ai - 票价等级
    seat_data += encode_string(10, "10")       # x - X坐标
    seat_data += encode_string(11, "20")       # y - Y坐标

    # 创建区域数据
    area_data = b''
    area_data += encode_string(1, "AREA_001")  # k - 区域ID
    area_data += encode_string(2, "VIP区域")    # a - 区域名称

    # 添加座位数据 (字段9)
    tag = (9 << 3) | 2  # wire_type = 2 for length-delimited
    area_data += encode_varint(tag) + encode_varint(len(seat_data)) + seat_data

    return area_data


def test_deserializer():
    """测试反序列化器"""
    print("=== Protocol Buffers 反序列化器测试 ===\n")

    # 创建测试数据
    test_data = create_test_data()
    print(f"测试数据长度: {len(test_data)} 字节")
    print(f"测试数据 (hex): {test_data.hex()}\n")

    # 反序列化
    deserializer = ProtobufDeserializer()
    try:
        area_data = deserializer.deserialize_show_area(test_data)

        print("=== 反序列化结果 ===")
        print(f"区域ID: {area_data.area_id}")
        print(f"区域名称: {area_data.area_name}")
        print(f"座位数量: {len(area_data.seats)}")

        if area_data.seats:
            seat = area_data.seats[0]
            print(f"\n第一个座位信息:")
            print(f"  ID: {seat.id}")
            print(f"  名称: {seat.name}")
            print(f"  排名: {seat.row_name}")
            print(f"  坐标: {seat.geometry}")
            print(f"  颜色: {seat.color}")
            print(f"  票价等级: {seat.fare_level}")
            print(f"  X坐标: {seat.x}")
            print(f"  Y坐标: {seat.y}")

        # 转换为JavaScript格式
        js_format = deserializer.to_javascript_format(area_data, "TEST_AREA")
        print(f"\n=== JavaScript格式输出 ===")
        if js_format:
            import json
            print(json.dumps(js_format[0], indent=2, ensure_ascii=False))

        print("\n✅ 测试成功!")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1:
        main()
    else:
        # 运行测试
        test_deserializer()
