# fetchAvailSeatData2 实现逻辑分析

## 概述

基于对`assets/onlineShowSeat_pb-ec199343.js`的分析，`fetchAvailSeatData2`函数使用Google Protocol Buffers进行数据反序列化。

## Protocol Buffers 消息结构

### 1. ShowAreaPb 消息结构

```protobuf
message ShowAreaPb {
  string k = 1;           // 区域ID/密钥
  string a = 2;           // 区域名称 (areaName)
  string n = 3;           // 可选字段
  string g = 4;           // 可选字段
  uint64 e = 5;           // 数值字段
  uint64 b = 6;           // 数值字段
  string i = 7;           // 可选字段
  string s = 8;           // 可选字段
  repeated ShowSeatPb ai = 9;  // 座位信息数组 (seatInfo)
}
```

### 2. ShowSeatPb 消息结构

```protobuf
message ShowSeatPb {
  string k = 1;           // 座位ID/密钥 (id)
  uint64 a = 2;           // 数值字段
  uint64 n = 3;           // 数值字段
  string g = 4;           // 几何坐标信息 (geometry/coordsStr)
  string e = 5;           // 颜色 (color)
  string b = 6;           // 座位名称 (name)
  string i = 7;           // 排名 (rowName)
  string s = 8;           // 可选字段
  string ai = 9;          // 票价等级 (fareLevel)
  string x = 10;          // X坐标
  string y = 11;          // Y坐标
}
```

## JavaScript 实现逻辑

### fetchAvailSeatData2 函数流程：

1. **API调用**: 
   ```javascript
   fetchSeatDataAPI({ areaId: u, showId: this._sid })
   ```

2. **动态导入Protocol Buffers模块**:
   ```javascript
   import("./onlineShowSeat_pb-ec199343.js")
   ```

3. **反序列化二进制数据**:
   ```javascript
   const deserializedData = protobufModule.default.ShowAreaPb
     .deserializeBinary(responseData)
     .toObject();
   ```

4. **数据转换处理**:
   ```javascript
   const processedSeats = deserializedData.ai.map((seat) => {
     const coordinates = seat.g.split(",").map((coord) => +coord);
     return {
       coords: [coordinates[0], coordinates[1]],
       coordsStr: seat.g,
       aid: areaId,
       color: seat.e,
       fareLevel: seat.ai,
       id: seat.k,
       rowName: seat.i,
       name: seat.b,
       areaName: deserializedData.a,
       x: seat.x ? +seat.x : undefined,
       y: seat.y ? +seat.y : undefined,
     };
   });
   ```

## deserializeBinary 实现原理

### 1. 二进制读取器初始化
```javascript
var binaryReader = new c.BinaryReader(binaryData);
var showAreaPb = new proto.tutorial.ShowAreaPb();
```

### 2. 字段解析循环
```javascript
for (; binaryReader.nextField() && !binaryReader.isEndGroup(); ) {
  var fieldNumber = binaryReader.getFieldNumber();
  switch (fieldNumber) {
    case 1: // k字段
      var value = binaryReader.readString();
      showAreaPb.setK(value);
      break;
    case 2: // a字段 (区域名称)
      var value = binaryReader.readString();
      showAreaPb.setA(value);
      break;
    // ... 其他字段
    case 9: // ai字段 (座位数组)
      var seatPb = new proto.tutorial.ShowSeatPb();
      binaryReader.readMessage(seatPb, proto.tutorial.ShowSeatPb.deserializeBinaryFromReader);
      showAreaPb.addAi(seatPb);
      break;
  }
}
```

### 3. 嵌套消息处理
对于字段9（座位数组），会递归调用`ShowSeatPb.deserializeBinaryFromReader`来解析每个座位对象。

### 4. 对象转换
最终通过`toObject()`方法将Protocol Buffers对象转换为普通JavaScript对象：

```javascript
{
  k: "区域ID",
  a: "区域名称",
  ai: [
    {
      k: "座位ID",
      g: "坐标字符串",
      e: "颜色",
      b: "座位名称",
      i: "排名",
      ai: "票价等级",
      x: "X坐标",
      y: "Y坐标"
    }
    // ... 更多座位
  ]
}
```

## 关键发现

1. **字段映射关系**:
   - `ShowAreaPb.a` → `areaName` (区域名称)
   - `ShowAreaPb.ai` → 座位信息数组
   - `ShowSeatPb.k` → `id` (座位ID)
   - `ShowSeatPb.g` → `coordsStr` (坐标字符串)
   - `ShowSeatPb.e` → `color` (颜色)
   - `ShowSeatPb.b` → `name` (座位名称)
   - `ShowSeatPb.i` → `rowName` (排名)
   - `ShowSeatPb.ai` → `fareLevel` (票价等级)

2. **数据类型**:
   - 大部分字段为字符串类型
   - 坐标信息存储为逗号分隔的字符串，需要解析为数组
   - 数值字段在JavaScript中会被转换为数字类型

3. **嵌套结构**:
   - `ShowAreaPb`包含一个`ShowSeatPb`的重复字段
   - 使用Protocol Buffers的标准嵌套消息处理方式

## 数据流程总结

```
API响应 (二进制数据)
    ↓
BinaryReader 初始化
    ↓
字段标签解析 (field_number + wire_type)
    ↓
根据字段类型读取数据
    ↓
嵌套消息递归处理
    ↓
Protocol Buffers 对象构建
    ↓
toObject() 转换为普通对象
    ↓
业务逻辑数据转换
    ↓
最终座位数据数组
```

这个分析为Python实现提供了完整的结构信息和处理逻辑。
