{"opFlag": true, "code": 200, "businessCode": 0, "msg": "SUCCESS", "timestamp": 1751559678158, "sid": "apibe7cec0149814f7a946fcf37b30d2d29", "data": [{"id": "1808803726947401729", "createDeptId": 227, "channelType": "2", "deptPaymentType": null, "merchantId": "1808801917394632706", "deptName": "看个比赛平台", "status": "1", "remark": null, "joinSettlement": true, "paymentMerchant": {"id": "1808801917394632706", "createDeptId": 227, "supplierId": "3", "paymentType": "12", "paymentTypeName": "支付宝支付", "paymentLogo": "https://res.dasheng.top/227/pro/pay/ali.png", "paymentDeptType": "1", "paymentDeptName": "看个比赛平台", "remark": null}}, {"id": "1831151527568064513", "createDeptId": 227, "channelType": "2", "deptPaymentType": null, "merchantId": "1831151283547652098", "deptName": "看个比赛平台", "status": "1", "remark": null, "joinSettlement": true, "paymentMerchant": {"id": "1831151283547652098", "createDeptId": 227, "supplierId": "2", "paymentType": "14", "paymentTypeName": "微信支付", "paymentLogo": "https://res.dasheng.top/227/pro/pay/wechat.png", "paymentDeptType": "1", "paymentDeptName": "看个比赛平台", "remark": null}}], "skipModel": null, "traceId": "", "cmsg": null}