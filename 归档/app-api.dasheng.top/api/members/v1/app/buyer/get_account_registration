{"opFlag": true, "code": 200, "businessCode": 0, "msg": "SUCCESS", "timestamp": 1751559678050, "sid": "api9fd82cb7bc5d44a29ff25629080d84fa", "data": {"id": "1927210399895928834", "realNameRelationId": null, "name": "吴祥龙", "sex": 0, "phone": "136****7810 ", "completePhone": "***********", "certificatesNo": "342***********3276", "phoneAreaCode": "+86", "mailAddr": "", "completeCertificatesNo": "342222198905113276", "certificatesType": "0", "isCurrentSelf": true, "isRealAuth": true, "isPhoneAuth": true, "createTime": "2025-05-27 11:48:59", "isDel": false, "isDisable": false, "updateTime": "2025-05-27 11:49:00", "currentIsExecuteThirdRealName": null, "createUserId": 119, "createDeptId": 227, "createBy": "KGBS-微信小程序-渠道"}, "skipModel": null, "traceId": "", "cmsg": null}