{"opFlag": true, "code": 200, "businessCode": 0, "msg": "SUCCESS", "timestamp": 1751559567398, "sid": "api986f1bf8ee814847a907928b338e35db", "data": {"id": "1939518495789092869", "name": "2025-07-05 19:35 徐州 VS 南通", "showNo": 4, "performanceId": "1931575051964022786", "hasSeat": "0", "showKey": "588B3430", "showTime": "2025-07-05 19:35:00", "showTimeEt": "2025-07-05 21:30:00", "showStatus": "4", "memberPreemptionTimeSt": null, "saleSt": "2025-07-03 21:00:00", "saleEt": "2025-07-05 20:30:00", "bgimgUrl": null, "webAllOrderSellableNumber": 6, "webPerOrderSellableNumber": 6, "allowCheckedTimes": 1, "displayShowName": "2025-07-05 19:35 徐州 VS 南通", "showFarelevelList": [{"id": null, "code": "A", "price": 5.0, "verboseName": "主队", "color": "#EA413A", "status": 0, "salableNum": 1, "isForbiden": true, "alt": "该区域不支持南通市身份证购买", "thirdId": null}, {"id": null, "code": "C", "price": 5.0, "verboseName": "客队", "color": "#F2D147", "status": 0, "salableNum": 0, "isForbiden": true, "alt": "该区域仅支持南通市身份证购买", "thirdId": null}], "showPolicyList": [{"id": "1939518496426627080", "policyType": "1", "name": "原价", "ticketNum": 1, "policySt": "2025-06-11 20:00:00", "policyEt": "2026-06-15 16:00:00", "remark": "原价"}, {"id": "1940332592691372040", "policyType": "1", "name": "客队", "ticketNum": 1, "policySt": "2025-07-01 21:00:00", "policyEt": "2025-07-05 20:30:00", "remark": "客队\n"}], "areaCount": 70, "areaId": null, "areaBgJsonUrlTemplet": "https://res.dasheng.top/227/pro/ticket/online_seat/ds_online_seat_1939518495789092869_$a.json", "hasSeatUnsoldSeatCount": 0, "areaImgUrl": null, "jsonUrl": "https://res.dasheng.top/227/pro/ticket/show_json/ds_show_1939518495789092869.json", "performanceSource": null, "thirdId": null, "choiceSeatType": 2, "ptInfo": {"ptProvinceCode": null, "ptDistrictCode": null, "ptCityCode": null, "ptAddress": null, "ptContact": null, "ptTel": null}, "printTicketTypesMap": "[{\"printTicketTypeValue\":\"身份证票\",\"printTicketTypeCode\":\"3\",\"printTicketTypeRemark\":\"现场刷身份证原件入场\"}]", "idCardTypesSaleable": [{"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "护照", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "3"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "台湾居民来往大陆通行证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "2"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "中国居民身份证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "0"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "港澳居民来往内地通行证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "1"}], "idCardTypesSaleableErrorMessage": "不支持您当前认证的证件类型购票", "isInSaleTime": true, "hasEntry": "0", "entryNote": null, "entryList": [], "pointSwitchOn": false, "minAvailablePoint": null, "maxPointDeductionPersent": null, "plugInType": 1, "douyinProductId": null, "openCalPriceImmediate": false, "isMinSaleStBeforeSale": false, "buyerLimitSwitchOn": false, "versionNo": 249, "createDeptId": 227}, "skipModel": null, "traceId": "", "cmsg": null}