{"opFlag": true, "code": 200, "businessCode": 0, "msg": "SUCCESS", "timestamp": 1751559580206, "sid": "apidd005aeeb0f74a30bdbb4734b9fae7d3", "data": {"id": "1939568417503854597", "name": "2025-07-06 19:30:00 南京城市 VS 延边龙鼎可喜安", "showNo": 9, "performanceId": "1896763345858736130", "hasSeat": "0", "showKey": "8D4d17d3", "showTime": "2025-07-06 19:30:00", "showTimeEt": "2025-07-06 21:30:00", "showStatus": "4", "memberPreemptionTimeSt": null, "saleSt": "2025-06-30 20:00:00", "saleEt": "2025-07-06 20:30:00", "bgimgUrl": null, "webAllOrderSellableNumber": 0, "webPerOrderSellableNumber": 6, "allowCheckedTimes": 1, "displayShowName": "2025-07-06 19:30:00 南京城市 VS 延边龙鼎可喜安", "showFarelevelList": [{"id": null, "code": "A", "price": 280.0, "verboseName": " ", "color": "#3180FF", "status": 0, "salableNum": 1, "isForbiden": false, "alt": null, "thirdId": null}, {"id": null, "code": "B", "price": 80.0, "verboseName": " ", "color": "#F45170", "status": 0, "salableNum": 1, "isForbiden": false, "alt": null, "thirdId": null}], "showPolicyList": [{"id": "1939568418791505926", "policyType": "1", "name": "原价", "ticketNum": 1, "policySt": "2025-03-06 00:00:00", "policyEt": "2025-11-16 19:59:59", "remark": ""}], "areaCount": 31, "areaId": null, "areaBgJsonUrlTemplet": "https://res.dasheng.top/227/pro/ticket/online_seat/ds_online_seat_1939568417503854597_$a.json", "hasSeatUnsoldSeatCount": 2, "areaImgUrl": null, "jsonUrl": "https://res.dasheng.top/227/pro/ticket/show_json/ds_show_1939568417503854597.json", "performanceSource": null, "thirdId": null, "choiceSeatType": 2, "ptInfo": {"ptProvinceCode": null, "ptDistrictCode": null, "ptCityCode": null, "ptAddress": null, "ptContact": null, "ptTel": null}, "printTicketTypesMap": "[{\"printTicketTypeValue\":\"身份证票\",\"printTicketTypeCode\":\"3\",\"printTicketTypeRemark\":\"现场刷身份证原件入场\"}]", "idCardTypesSaleable": [{"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "中国居民身份证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "0"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "港澳居民来往内地通行证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "1"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "台湾居民来往大陆通行证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "2"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "外国人永久居留身份证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "4"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "港澳台居民居住证", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "5"}, {"pageSize": 20, "pageNum": 1, "sortOrder": null, "createBy": null, "updateBy": null, "updateTime": null, "createTime": null, "createUserId": null, "updateUserId": null, "createDeptId": null, "isDel": false, "memberInfoId": null, "memberAccountId": null, "remark": "护照", "scopeData": null, "createDeptIds": null, "requestIp": null, "id": null, "confId": null, "idcardType": "3"}], "idCardTypesSaleableErrorMessage": "不支持您当前认证的证件类型购票", "isInSaleTime": true, "hasEntry": "0", "entryNote": null, "entryList": [], "pointSwitchOn": false, "minAvailablePoint": null, "maxPointDeductionPersent": null, "plugInType": null, "douyinProductId": null, "openCalPriceImmediate": false, "isMinSaleStBeforeSale": false, "buyerLimitSwitchOn": false, "versionNo": 1, "createDeptId": 227}, "skipModel": null, "traceId": "", "cmsg": null}