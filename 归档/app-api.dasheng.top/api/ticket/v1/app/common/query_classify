{"opFlag": true, "code": 200, "businessCode": 0, "msg": "SUCCESS", "timestamp": 1751559570843, "sid": "api743933181fa84a5e9baa8f9b5d49425c", "data": [{"createTime": "2022-12-31 17:12:59", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2023-12-27 18:52:18", "updateBy": "WAGadmin", "updateUserId": 503, "isDel": false, "id": "1117", "name": "足球", "homepageDisplay": "1", "parentId": "11", "sortNum": 1, "icon": "https://res.dasheng.top/ctms/20231114/ENV_PRO/png/ds_70ca953841574f14a9fe157132e68c03.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-11-22 12:43:02", "createBy": "admin", "createUserId": 1, "createDeptId": 103, "updateTime": "2023-06-06 13:17:24", "updateBy": "xcxAdmin", "updateUserId": 120, "isDel": false, "id": "11", "name": "球类运动", "homepageDisplay": "0", "parentId": "00", "sortNum": 1, "icon": "https://res.dasheng.top/ctms/ds_dafcea298081468695da223159ae42bd.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-12-28 15:18:20", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2023-12-27 18:52:24", "updateBy": "WAGadmin", "updateUserId": 503, "isDel": false, "id": "1114", "name": "篮球", "homepageDisplay": "1", "parentId": "11", "sortNum": 2, "icon": "https://res.dasheng.top/ctms/20231114/ENV_PRO/png/ds_34f5f8ab0aee493ab22c2066245a73bc.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-11-21 15:42:20", "createBy": "admin", "createUserId": 1, "createDeptId": 103, "updateTime": "2024-01-22 14:48:03", "updateBy": "yunying-3", "updateUserId": 225, "isDel": false, "id": "10", "name": "冰雪", "homepageDisplay": "0", "parentId": "00", "sortNum": 2, "icon": "https://res.dasheng.top/ctms/ds_f33d3c0592f643fb85ac26452477cf18.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-11-22 12:43:18", "createBy": "admin", "createUserId": 1, "createDeptId": 103, "updateTime": "2025-06-11 15:01:02", "updateBy": "z<PERSON><PERSON><PERSON>", "updateUserId": 125, "isDel": false, "id": "12", "name": "水上运动", "homepageDisplay": "0", "parentId": "00", "sortNum": 3, "icon": "https://res.dasheng.top/ctms/ds_218af1acf1a9441bb3e25f13dbdd1dd6.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-11-22 12:43:47", "createBy": "admin", "createUserId": 1, "createDeptId": 103, "updateTime": "2024-12-09 14:12:34", "updateBy": "admin", "updateUserId": 1, "isDel": false, "id": "1110", "name": "羽毛球", "homepageDisplay": "0", "parentId": "11", "sortNum": 3, "icon": "https://res.dasheng.top/227/pro/ctms_tool/20241209/png/125aa3c7a01b4a2095596dc78c2fbcdd.png", "remark": "测试", "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-12-31 16:55:57", "createBy": "yunying-1", "createUserId": 123, "createDeptId": 227, "updateTime": "2023-11-01 14:25:29", "updateBy": "yunying-3", "updateUserId": 225, "isDel": false, "id": "1116", "name": "网球", "homepageDisplay": "0", "parentId": "11", "sortNum": 3, "icon": "https://res.dasheng.top/227/pro/ctms_tool/20240823/png/4b0de3c2f24e4d88a603fa9d426116a5.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2023-03-10 14:57:21", "createBy": "yunying-2", "createUserId": 124, "createDeptId": 227, "updateTime": "2024-11-08 09:47:32", "updateBy": "admin", "updateUserId": 1, "isDel": false, "id": "19", "name": "马拉松", "homepageDisplay": "1", "parentId": "18", "sortNum": 3, "icon": "https://res.dasheng.top/227/pro/ctms_tool/20240730/jpg/3d1dfbfcf8964f348365ba94c7331b95.jpg", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2023-02-15 10:41:52", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2023-06-07 10:24:37", "updateBy": "xcxAdmin", "updateUserId": 120, "isDel": false, "id": "18", "name": "更多", "homepageDisplay": "0", "parentId": "00", "sortNum": 4, "icon": "https://res.dasheng.top/ctms/ds_0dfcb973404d41c98897e96782e68833.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2023-02-13 16:10:37", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2024-06-14 12:23:43", "updateBy": "yunying-4", "updateUserId": 226, "isDel": false, "id": "1123", "name": "排球", "homepageDisplay": "1", "parentId": "11", "sortNum": 4, "icon": "https://res.dasheng.top/ctms/20231114/ENV_PRO/png/ds_b1844f1fc3c3471eb7e37e74fb03a805.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2022-12-28 16:12:04", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2024-06-14 12:23:24", "updateBy": "yunying-4", "updateUserId": 226, "isDel": false, "id": "1115", "name": "乒乓球", "homepageDisplay": "0", "parentId": "11", "sortNum": 4, "icon": "https://res.dasheng.top/227/pro/ctms_tool/20240509/png/801c796ef0c04a01ba1c8b1522afb627.png", "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2024-10-29 18:22:18", "createBy": "admin", "createUserId": 1, "createDeptId": 100, "updateTime": "2024-10-29 18:22:18", "updateBy": null, "updateUserId": null, "isDel": false, "id": "40", "name": "电子竞技", "homepageDisplay": "0", "parentId": "00", "sortNum": 40, "icon": null, "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2024-10-24 15:09:52", "createBy": "KGBS01", "createUserId": 844, "createDeptId": 100, "updateTime": "2024-10-24 15:13:57", "updateBy": "KGBS01", "updateUserId": 844, "isDel": false, "id": "39", "name": "三亚民运会", "homepageDisplay": "0", "parentId": "00", "sortNum": 101, "icon": null, "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2025-05-28 18:25:02", "createBy": "xcxAdmin", "createUserId": 120, "createDeptId": 227, "updateTime": "2025-05-28 18:25:02", "updateBy": null, "updateUserId": null, "isDel": false, "id": "1217", "name": "游泳", "homepageDisplay": "0", "parentId": "12", "sortNum": 1222, "icon": null, "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}, {"createTime": "2025-06-12 16:47:21", "createBy": "admin", "createUserId": 1, "createDeptId": 100, "updateTime": "2025-06-12 16:57:59", "updateBy": "xcxAdmin", "updateUserId": 120, "isDel": false, "id": "4011", "name": "CSGO", "homepageDisplay": "1", "parentId": "40", "sortNum": 4020, "icon": null, "remark": null, "isAvailable": "1", "children": null, "pageSize": 20}], "skipModel": null, "traceId": "", "cmsg": null}