// 反混淆后的 PickAreaSeat JavaScript 文件
// 主要功能：座位区域选择和数据处理

// 常量定义
const CHUNK_SIZE = 4;
const OPACITY_VALUE = 0.6;
const MAX_VALUE = 40;
const XOR_KEY = 22; // 用于数据解密的XOR密钥

// Snappy压缩算法中使用的掩码
const MASKS = [0, 255, 65535, 16777215, 4294967295];

// 座位相关常量
const SEAT_TYPE = "seatType";
const AREA_TYPE = "areaType";
const SEAT_CHECK = "seatCheck";
const BACKGROUND = "background";
const BACKGROUND_IMAGE = "backgroundImage";

// 模拟导入的函数 (这些在实际代码中来自其他模块)
function createReactiveRef(initialValue) {
  return { value: initialValue };
}

function fetchSeatDataAPI(params) {
  // 模拟API调用 - 返回Protocol Buffers数据
  return fetch('/api/seat-data-pb', {
    method: 'POST',
    body: JSON.stringify(params)
  }).then(response => response.arrayBuffer());
}

function fetchCompressedSeatDataAPI(params) {
  // 模拟API调用 - 返回压缩加密的数据
  return fetch('/api/seat-data-compressed', {
    method: 'POST',
    body: JSON.stringify(params)
  }).then(response => response.arrayBuffer());
}

// Snappy 压缩算法实现
class SnappyDecompressor {
  constructor(array) {
    this.array = array;
    this.pos = 0;
  }

  readUncompressedLength() {
    let result = 0;
    let shift = 0;
    let byte, value;
    
    while (shift < 32 && this.pos < this.array.length) {
      byte = this.array[this.pos];
      this.pos += 1;
      value = byte & 127;
      
      if ((value << shift) >>> shift !== value) {
        return -1; // 溢出
      }
      
      result |= value << shift;
      if (byte < 128) return result;
      shift += 7;
    }
    return -1;
  }

  uncompressToBuffer(outputBuffer) {
    const input = this.array;
    const inputLength = input.length;
    let inputPos = this.pos;
    let outputPos = 0;
    let command, length, offset;

    while (inputPos < input.length) {
      command = input[inputPos];
      inputPos += 1;

      if (command & 3) {
        // 复制命令
        switch (command & 3) {
          case 1:
            length = ((command >>> 2) & 7) + 4;
            offset = input[inputPos] + ((command >>> 5) << 8);
            inputPos += 1;
            break;
          case 2:
            if (inputPos + 1 >= inputLength) return false;
            length = (command >>> 2) + 1;
            offset = input[inputPos] + (input[inputPos + 1] << 8);
            inputPos += 2;
            break;
          case 3:
            if (inputPos + 3 >= inputLength) return false;
            length = (command >>> 2) + 1;
            offset = input[inputPos] + (input[inputPos + 1] << 8) + 
                    (input[inputPos + 2] << 16) + (input[inputPos + 3] << 24);
            inputPos += 4;
            break;
        }
        
        if (offset === 0 || offset > outputPos) return false;
        
        // 复制数据
        for (let i = 0; i < length; i++) {
          outputBuffer[outputPos + i] = outputBuffer[outputPos - offset + i];
        }
        outputPos += length;
      } else {
        // 字面量命令
        length = (command >>> 2) + 1;
        if (length > 60) {
          if (inputPos + 3 >= inputLength) return false;
          const extraBytes = length - 60;
          length = input[inputPos] + (input[inputPos + 1] << 8) + 
                  (input[inputPos + 2] << 16) + (input[inputPos + 3] << 24);
          length = (length & MASKS[extraBytes]) + 1;
          inputPos += extraBytes;
        }
        
        if (inputPos + length > inputLength) return false;
        
        // 复制字面量数据
        for (let i = 0; i < length; i++) {
          outputBuffer[outputPos + i] = input[inputPos + i];
        }
        inputPos += length;
        outputPos += length;
      }
    }
    return true;
  }
}

// Snappy 解压缩函数
function snappyUncompress(compressedData, maxLength) {
  if (!isValidInput(compressedData)) {
    throw new TypeError("Argument must be ArrayBuffer, Buffer, or Uint8Array");
  }

  let isUint8Array = false;
  let isArrayBuffer = false;
  
  if (compressedData instanceof Uint8Array) {
    isUint8Array = true;
  } else if (compressedData instanceof ArrayBuffer) {
    isArrayBuffer = true;
    compressedData = new Uint8Array(compressedData);
  }

  const decompressor = new SnappyDecompressor(compressedData);
  const uncompressedLength = decompressor.readUncompressedLength();
  
  if (uncompressedLength === -1) {
    throw new Error("Invalid Snappy bitstream");
  }
  
  if (uncompressedLength > maxLength) {
    throw new Error(`Uncompressed length ${uncompressedLength} exceeds maximum ${maxLength}`);
  }

  let outputBuffer;
  if (isUint8Array) {
    outputBuffer = new Uint8Array(uncompressedLength);
    if (!decompressor.uncompressToBuffer(outputBuffer)) {
      throw new Error("Invalid Snappy bitstream");
    }
  } else if (isArrayBuffer) {
    outputBuffer = new ArrayBuffer(uncompressedLength);
    const view = new Uint8Array(outputBuffer);
    if (!decompressor.uncompressToBuffer(view)) {
      throw new Error("Invalid Snappy bitstream");
    }
  } else {
    outputBuffer = Buffer.alloc(uncompressedLength);
    if (!decompressor.uncompressToBuffer(outputBuffer)) {
      throw new Error("Invalid Snappy bitstream");
    }
  }

  return outputBuffer;
}

// 主要的座位数据处理类
class SeatDataManager {
  constructor(showId) {
    this._showId = showId;
    this._seats = Object.create(null);
    this._availableSeats = Object.create(null);
    this.seatIconList = null;
    this.areaSeatTemplate = "";
    this.isLive = false;
    this.hasAvailableSeats = false;
    this.versionNumber = 0;
    this.areaObject = createReactiveRef({});
    this.isAreaLoaded = false;
    this.areaResolveQueue = new Map();
  }

  // 获取可用座位数据的方法1 - 使用Protocol Buffers
  async fetchAvailSeatData2(areaId) {
    return new Promise((resolve, reject) => {
      // 调用API获取数据
      fetchSeatDataAPI({ areaId: areaId, showId: this._showId })
        .then((responseData) => {
          // 动态导入Protocol Buffers模块
          import("./onlineShowSeat_pb-ec199343.js")
            .then((protobufModule) => {
              // 使用Protocol Buffers反序列化数据
              const deserializedData = protobufModule.default.ShowAreaPb
                .deserializeBinary(responseData)
                .toObject();
              
              // 处理座位数据
              const processedSeats = deserializedData.seatInfo.map((seat) => {
                const coordinates = seat.geometry.split(",").map((coord) => +coord);
                return {
                  coords: [coordinates[0], coordinates[1]],
                  coordsStr: seat.geometry,
                  areaId: areaId,
                  color: seat.color,
                  fareLevel: seat.fareLevel,
                  id: seat.key,
                  rowName: seat.rowName,
                  name: seat.name,
                  areaName: deserializedData.areaName,
                  x: seat.x ? +seat.x : undefined,
                  y: seat.y ? +seat.y : undefined,
                };
              });
              
              resolve(processedSeats);
            });
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 获取可用座位数据的方法2 - 使用XOR解密和Snappy解压缩
  async fetchAvailSeatData(areaId) {
    return new Promise((resolve, reject) => {
      // 调用另一个API获取加密压缩的数据
      fetchCompressedSeatDataAPI({ areaId: areaId, showId: this._showId })
        .then((encryptedData) => {
          // 步骤1: XOR解密
          const decryptedData = new Uint8Array(encryptedData).map((byte) => byte ^ XOR_KEY);
          
          // 步骤2: Snappy解压缩
          const decompressedData = snappyUncompress(decryptedData);
          
          // 步骤3: 解码为文本
          const textDecoder = new TextDecoder();
          const jsonString = textDecoder.decode(decompressedData);
          
          // 步骤4: 解析JSON
          const parsedData = JSON.parse(jsonString);
          
          // 步骤5: 处理座位信息
          const processedSeats = parsedData.seatInfo?.map((seat) => {
            const coordinates = seat.geometry.split(",").map((coord) => +coord);
            return {
              coords: [coordinates[0], coordinates[1]],
              coordsStr: seat.geometry,
              areaId: areaId,
              color: seat.color,
              fareLevel: seat.fareLevel,
              id: seat.key,
              rowName: seat.rowName,
              name: seat.name,
              areaName: parsedData.areaName,
              x: seat.x ? +seat.x : undefined,
              y: seat.y ? +seat.y : undefined,
            };
          });
          
          resolve(processedSeats || []);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 其他辅助方法...
  async fetchAreaData(areaId) {
    if (this.isAreaLoaded) {
      return areaId ? this.areaObject[areaId] || null : this.areaObject;
    }
    
    if (areaId && this.areaObject[areaId]) {
      return this.areaObject[areaId];
    }
    
    return new Promise((resolve) => {
      this.areaResolveQueue.set(areaId || "", resolve);
    });
  }

  setAreaSeatTemplate(template) {
    this.areaSeatTemplate = template;
  }
}

// 工具函数
function isValidInput(data) {
  return data instanceof Uint8Array || 
         data instanceof ArrayBuffer || 
         (typeof Buffer !== 'undefined' && Buffer.isBuffer(data));
}

// Protocol Buffers 反序列化实现分析
/*
deserializeBinary 的实现原理：

1. Protocol Buffers 是Google开发的序列化格式
2. deserializeBinary 方法将二进制数据转换为JavaScript对象
3. 序列化过程包括：
   - 字段编号和类型的编码
   - 变长整数编码 (varint)
   - 字符串的UTF-8编码
   - 嵌套消息的递归处理

具体的序列化过程：
1. 读取字段标签 (field tag) = (field_number << 3) | wire_type
2. 根据wire_type确定如何读取数据：
   - 0: varint (int32, int64, uint32, uint64, sint32, sint64, bool, enum)
   - 1: 64-bit (fixed64, sfixed64, double)
   - 2: length-delimited (string, bytes, embedded messages, packed repeated fields)
   - 3: start group (deprecated)
   - 4: end group (deprecated)
   - 5: 32-bit (fixed32, sfixed32, float)
3. 解析字段值并存储到对象中
4. 重复直到所有数据被处理

在这个座位系统中，ShowAreaPb 可能包含以下字段：
- areaName (string): 区域名称
- seatInfo (repeated message): 座位信息数组
  - key (string): 座位ID
  - name (string): 座位名称
  - rowName (string): 排名
  - geometry (string): 坐标信息
  - color (string): 颜色
  - fareLevel (int32): 票价等级
  - x, y (optional int32): 坐标位置
*/

// 模拟 Protocol Buffers 反序列化的简化实现
class ProtoBufDeserializer {
  constructor(binaryData) {
    this.data = new Uint8Array(binaryData);
    this.position = 0;
  }

  // 读取变长整数
  readVarint() {
    let result = 0;
    let shift = 0;

    while (this.position < this.data.length) {
      const byte = this.data[this.position++];
      result |= (byte & 0x7F) << shift;

      if ((byte & 0x80) === 0) {
        return result;
      }

      shift += 7;
      if (shift >= 32) {
        throw new Error("Varint too long");
      }
    }

    throw new Error("Unexpected end of data");
  }

  // 读取长度分隔的数据
  readLengthDelimited() {
    const length = this.readVarint();
    if (this.position + length > this.data.length) {
      throw new Error("Not enough data");
    }

    const result = this.data.slice(this.position, this.position + length);
    this.position += length;
    return result;
  }

  // 读取字符串
  readString() {
    const bytes = this.readLengthDelimited();
    return new TextDecoder('utf-8').decode(bytes);
  }

  // 读取字段标签
  readTag() {
    const tag = this.readVarint();
    return {
      fieldNumber: tag >>> 3,
      wireType: tag & 0x7
    };
  }

  // 解析ShowAreaPb消息
  parseShowAreaPb() {
    const result = {
      areaName: '',
      seatInfo: []
    };

    while (this.position < this.data.length) {
      const tag = this.readTag();

      switch (tag.fieldNumber) {
        case 1: // areaName
          if (tag.wireType === 2) { // length-delimited
            result.areaName = this.readString();
          }
          break;

        case 2: // seatInfo (repeated)
          if (tag.wireType === 2) { // length-delimited
            const seatData = this.readLengthDelimited();
            const seatDeserializer = new ProtoBufDeserializer(seatData);
            result.seatInfo.push(seatDeserializer.parseSeatInfo());
          }
          break;

        default:
          // 跳过未知字段
          this.skipField(tag.wireType);
          break;
      }
    }

    return result;
  }

  // 解析座位信息
  parseSeatInfo() {
    const seat = {
      key: '',
      name: '',
      rowName: '',
      geometry: '',
      color: '',
      fareLevel: 0,
      x: undefined,
      y: undefined
    };

    while (this.position < this.data.length) {
      const tag = this.readTag();

      switch (tag.fieldNumber) {
        case 1: // key
          seat.key = this.readString();
          break;
        case 2: // name
          seat.name = this.readString();
          break;
        case 3: // rowName
          seat.rowName = this.readString();
          break;
        case 4: // geometry
          seat.geometry = this.readString();
          break;
        case 5: // color
          seat.color = this.readString();
          break;
        case 6: // fareLevel
          seat.fareLevel = this.readVarint();
          break;
        case 7: // x
          seat.x = this.readVarint();
          break;
        case 8: // y
          seat.y = this.readVarint();
          break;
        default:
          this.skipField(tag.wireType);
          break;
      }
    }

    return seat;
  }

  // 跳过字段
  skipField(wireType) {
    switch (wireType) {
      case 0: // varint
        this.readVarint();
        break;
      case 1: // 64-bit
        this.position += 8;
        break;
      case 2: // length-delimited
        this.readLengthDelimited();
        break;
      case 5: // 32-bit
        this.position += 4;
        break;
      default:
        throw new Error(`Unknown wire type: ${wireType}`);
    }
  }
}

// 数据处理流程总结
/*
整个数据处理流程：

方法1 (fetchAvailSeatData2):
API响应 → Protocol Buffers二进制数据 → deserializeBinary() → JavaScript对象 → 座位数据数组

方法2 (fetchAvailSeatData):
API响应 → 加密的压缩数据 → XOR解密 → Snappy解压缩 → JSON字符串 → 解析 → 座位数据数组

两种方法的区别：
- 方法1使用Protocol Buffers，数据更紧凑，解析更高效
- 方法2使用JSON + 压缩 + 加密，更容易调试但处理步骤更多

XOR解密的关键：
- 使用固定密钥22对每个字节进行异或运算
- 这是一种简单的对称加密方式

Snappy压缩的特点：
- Google开发的快速压缩算法
- 优化了解压缩速度而非压缩率
- 适合实时数据传输场景
*/

// 导出主类和工具类
export { SeatDataManager as default, ProtoBufDeserializer, SnappyDecompressor };
