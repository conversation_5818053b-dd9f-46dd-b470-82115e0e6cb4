# Sg(t) 函数详细分析

## 函数概述

`Sg(t)` 函数是一个用于计算对象哈希值的函数，主要用于生成对象的唯一标识符。它通过对对象进行标准化排序，然后序列化并计算哈希值来实现。

## 完整的计算流程

### 1. 输入验证
```javascript
if (t && ln(t)) {
    // 继续处理
}
```
- 检查输入 `t` 是否存在（非null、非undefined）
- 使用 `ln(t)` 函数检查是否为纯对象（不是数组、函数等）

### 2. 对象标准化排序 `br(t)`
这是核心的排序函数，确保相同内容的对象产生相同的哈希值：

#### 2.1 键排序
```javascript
p = Object.keys(t).sort();  // 获取所有键并按字母顺序排序
```

#### 2.2 递归处理值
对每个键的值进行分类处理：
- **嵌套对象**: 递归调用 `br()` 函数
- **数组**: 遍历数组，对数组中的对象元素递归排序
- **基本类型**: 直接赋值

### 3. JSON序列化
```javascript
const p = JSON.stringify(f) + bg;
```
- 将排序后的对象转换为JSON字符串
- 添加固定后缀 `"ctms"`

### 4. 哈希计算
```javascript
e = Cg(p).toString();
```
- 对序列化字符串计算哈希值（`Cg` 函数，可能是MD5、SHA等）

## 详细示例分析

### 示例1：简单对象
**输入**:
```javascript
{
  "name": "张三",
  "age": 25,
  "city": "北京"
}
```

**处理过程**:
1. **键排序**: `["age", "city", "name"]`
2. **重构对象**: 
   ```javascript
   {
     "age": 25,
     "city": "北京", 
     "name": "张三"
   }
   ```
3. **序列化**: `{"age":25,"city":"北京","name":"张三"}ctms`
4. **哈希值**: `6e180b1ed062922279c1239553b0cab2`

### 示例2：嵌套对象
**输入**:
```javascript
{
  "user": {
    "name": "李四",
    "profile": {
      "age": 30,
      "email": "<EMAIL>"
    }
  },
  "settings": {
    "theme": "dark",
    "language": "zh-CN"
  }
}
```

**处理过程**:
1. **顶层键排序**: `["settings", "user"]`
2. **递归排序嵌套对象**:
   - `user` 对象: `["name", "profile"]`
   - `profile` 对象: `["age", "email"]`
   - `settings` 对象: `["language", "theme"]`
3. **最终结构**:
   ```javascript
   {
     "settings": {
       "language": "zh-CN",
       "theme": "dark"
     },
     "user": {
       "name": "李四",
       "profile": {
         "age": 30,
         "email": "<EMAIL>"
       }
     }
   }
   ```

### 示例3：包含数组的对象
**输入**:
```javascript
{
  "items": [
    {"id": 1, "name": "商品A"},
    {"id": 2, "name": "商品B"}
  ],
  "total": 2,
  "category": "电子产品"
}
```

**处理过程**:
1. **顶层键排序**: `["category", "items", "total"]`
2. **数组处理**: 对数组中的每个对象进行键排序
   - 第一个对象: `{"id": 1, "name": "商品A"}`
   - 第二个对象: `{"id": 2, "name": "商品B"}`
3. **最终结构**:
   ```javascript
   {
     "category": "电子产品",
     "items": [
       {"id": 1, "name": "商品A"},
       {"id": 2, "name": "商品B"}
     ],
     "total": 2
   }
   ```

## 关键特性

### 1. 确定性输出
相同内容的对象，无论键的原始顺序如何，都会产生相同的哈希值：
```javascript
// 这两个对象会产生相同的哈希值
obj1 = {"b": 2, "a": 1, "c": 3}
obj2 = {"a": 1, "c": 3, "b": 2}
```

### 2. 递归处理
函数能够处理任意深度的嵌套对象和数组。

### 3. 类型安全
只处理纯对象，其他类型（数组、函数、null等）返回空字符串。

## Python实现要点

### 1. 对象检查
```python
def is_plain_object(obj: Any) -> bool:
    return isinstance(obj, dict)
```

### 2. 递归排序
```python
def sort_object_recursively(obj: Any, order: str = "asc") -> Any:
    if not is_plain_object(obj):
        return obj
    
    result = {}
    keys = sorted(obj.keys())
    
    for key in keys:
        value = obj[key]
        if is_plain_object(value):
            result[key] = sort_object_recursively(value, order)
        elif isinstance(value, list):
            result[key] = [
                sort_object_recursively(item, order) if is_plain_object(item) else item
                for item in value
            ]
        else:
            result[key] = value
    
    return result
```

### 3. 主函数
```python
def sg_function(input_obj: Any) -> str:
    result = ""
    
    if input_obj and is_plain_object(input_obj):
        sorted_obj = sort_object_recursively(input_obj)
        
        if sorted_obj:
            json_str = json.dumps(sorted_obj, separators=(',', ':'), ensure_ascii=False)
            text_with_suffix = json_str + "ctms"
            result = hashlib.md5(text_with_suffix.encode('utf-8')).hexdigest()
    
    return result
```

## 使用场景

1. **API签名**: 为API请求参数生成签名
2. **缓存键**: 为复杂对象生成缓存键
3. **数据完整性**: 检查数据是否被修改
4. **去重**: 识别相同内容的对象

## 注意事项

1. **哈希函数**: 实际的 `Cg` 函数可能不是MD5，需要根据具体实现调整
2. **字符编码**: 确保JSON序列化的字符编码一致
3. **浮点数精度**: 浮点数的序列化可能因精度不同产生不同结果
4. **时间复杂度**: 对于大型嵌套对象，排序操作可能较慢

这个函数设计巧妙，通过标准化对象结构来确保相同内容产生相同哈希值，是一个很好的对象指纹生成方案。
