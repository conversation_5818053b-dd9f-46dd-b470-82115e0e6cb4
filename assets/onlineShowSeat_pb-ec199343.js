import { G as Po, J as z } from "./index-f027b512.js";
var po = {};
(function (h) {
  var y =
      typeof Object.defineProperties == "function"
        ? Object.defineProperty
        : function (t, o, e) {
            t != Array.prototype && t != Object.prototype && (t[o] = e.value);
          },
    g =
      typeof window != "undefined" && window === z
        ? z
        : typeof z != "undefined" && z != null
        ? z
        : z;
  function A(t, o) {
    if (o) {
      var e = g;
      t = t.split(".");
      for (var r = 0; r < t.length - 1; r++) {
        var n = t[r];
        n in e || (e[n] = {}), (e = e[n]);
      }
      (t = t[t.length - 1]),
        (r = e[t]),
        (o = o(r)),
        o != r &&
          o != null &&
          y(e, t, { configurable: !0, writable: !0, value: o });
    }
  }
  function so(t) {
    var o = 0;
    return function () {
      return o < t.length ? { done: !1, value: t[o++] } : { done: !0 };
    };
  }
  function jt() {
    (jt = function () {}), g.Symbol || (g.Symbol = ao);
  }
  function kt(t, o) {
    (this.a = t),
      y(this, "description", { configurable: !0, writable: !0, value: o });
  }
  kt.prototype.toString = function () {
    return this.a;
  };
  var ao = (function () {
    function t(e) {
      if (this instanceof t) throw new TypeError("Symbol is not a constructor");
      return new kt("jscomp_symbol_" + (e || "") + "_" + o++, e);
    }
    var o = 0;
    return t;
  })();
  function gt() {
    jt();
    var t = g.Symbol.iterator;
    t || (t = g.Symbol.iterator = g.Symbol("Symbol.iterator")),
      typeof Array.prototype[t] != "function" &&
        y(Array.prototype, t, {
          configurable: !0,
          writable: !0,
          value: function () {
            return uo(so(this));
          },
        }),
      (gt = function () {});
  }
  function uo(t) {
    return (
      gt(),
      (t = { next: t }),
      (t[g.Symbol.iterator] = function () {
        return this;
      }),
      t
    );
  }
  function ho(t, o) {
    gt(), t instanceof String && (t += "");
    var e = 0,
      r = {
        next: function () {
          if (e < t.length) {
            var n = e++;
            return { value: o(n, t[n]), done: !1 };
          }
          return (
            (r.next = function () {
              return { done: !0, value: void 0 };
            }),
            r.next()
          );
        },
      };
    return (
      (r[Symbol.iterator] = function () {
        return r;
      }),
      r
    );
  }
  A("Array.prototype.entries", function (t) {
    return (
      t ||
      function () {
        return ho(this, function (o, e) {
          return [o, e];
        });
      }
    );
  });
  var lo = z || self;
  function v(t, o, e) {
    (t = t.split(".")),
      (e = e || lo),
      t[0] in e ||
        typeof e.execScript == "undefined" ||
        e.execScript("var " + t[0]);
    for (var r; t.length && (r = t.shift()); )
      t.length || o === void 0
        ? e[r] && e[r] !== Object.prototype[r]
          ? (e = e[r])
          : (e = e[r] = {})
        : (e[r] = o);
  }
  function D(t) {
    var o = typeof t;
    if (o == "object")
      if (t) {
        if (t instanceof Array) return "array";
        if (t instanceof Object) return o;
        var e = Object.prototype.toString.call(t);
        if (e == "[object Window]") return "object";
        if (
          e == "[object Array]" ||
          (typeof t.length == "number" &&
            typeof t.splice != "undefined" &&
            typeof t.propertyIsEnumerable != "undefined" &&
            !t.propertyIsEnumerable("splice"))
        )
          return "array";
        if (
          e == "[object Function]" ||
          (typeof t.call != "undefined" &&
            typeof t.propertyIsEnumerable != "undefined" &&
            !t.propertyIsEnumerable("call"))
        )
          return "function";
      } else return "null";
    else if (o == "function" && typeof t.call == "undefined") return "object";
    return o;
  }
  function Et(t) {
    var o = typeof t;
    return (o == "object" && t != null) || o == "function";
  }
  function fo(t, o, e) {
    v(t, o, e);
  }
  function yo(t, o) {
    function e() {}
    (e.prototype = o.prototype),
      (t.prototype = new e()),
      (t.prototype.constructor = t);
  }
  var xt =
    "constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(
      " "
    );
  function co(t, o) {
    for (var e, r, n = 1; n < arguments.length; n++) {
      r = arguments[n];
      for (e in r) t[e] = r[e];
      for (var s = 0; s < xt.length; s++)
        (e = xt[s]),
          Object.prototype.hasOwnProperty.call(r, e) && (t[e] = r[e]);
    }
  }
  var go = Array.prototype.forEach
      ? function (t, o) {
          Array.prototype.forEach.call(t, o, void 0);
        }
      : function (t, o) {
          for (
            var e = t.length, r = typeof t == "string" ? t.split("") : t, n = 0;
            n < e;
            n++
          )
            n in r && o.call(void 0, r[n], n, t);
        },
    Z = Array.prototype.map
      ? function (t, o) {
          return Array.prototype.map.call(t, o, void 0);
        }
      : function (t, o) {
          for (
            var e = t.length,
              r = Array(e),
              n = typeof t == "string" ? t.split("") : t,
              s = 0;
            s < e;
            s++
          )
            s in n && (r[s] = o.call(void 0, n[s], s, t));
          return r;
        };
  function vo(t, o, e) {
    return 2 >= arguments.length
      ? Array.prototype.slice.call(t, o)
      : Array.prototype.slice.call(t, o, e);
  }
  function it(t, o, e, r) {
    var n = "Assertion failed";
    if (e) {
      n += ": " + e;
      var s = r;
    } else t && ((n += ": " + t), (s = o));
    throw Error(n, s || []);
  }
  function u(t, o, e) {
    for (var r = [], n = 2; n < arguments.length; ++n) r[n - 2] = arguments[n];
    return t || it("", null, o, r), t;
  }
  function So(t, o, e) {
    for (var r = [], n = 2; n < arguments.length; ++n) r[n - 2] = arguments[n];
    typeof t != "string" &&
      it("Expected string but got %s: %s.", [D(t), t], o, r);
  }
  function wo(t, o, e) {
    for (var r = [], n = 2; n < arguments.length; ++n) r[n - 2] = arguments[n];
    Array.isArray(t) || it("Expected array but got %s: %s.", [D(t), t], o, r);
  }
  function B(t, o) {
    for (var e = [], r = 1; r < arguments.length; ++r) e[r - 1] = arguments[r];
    throw Error("Failure" + (t ? ": " + t : ""), e);
  }
  function O(t, o, e, r) {
    for (var n = [], s = 3; s < arguments.length; ++s) n[s - 3] = arguments[s];
    t instanceof o ||
      it("Expected instanceof %s but got %s.", [Bt(o), Bt(t)], e, n);
  }
  function Bt(t) {
    return t instanceof Function
      ? t.displayName || t.name || "unknown type name"
      : t instanceof Object
      ? t.constructor.displayName ||
        t.constructor.name ||
        Object.prototype.toString.call(t)
      : t === null
      ? "null"
      : typeof t;
  }
  function P(t, o) {
    if (
      ((this.c = t),
      (this.b = o),
      (this.a = {}),
      (this.arrClean = !0),
      0 < this.c.length)
    ) {
      for (t = 0; t < this.c.length; t++) {
        o = this.c[t];
        var e = o[0];
        this.a[e.toString()] = new It(e, o[1]);
      }
      this.arrClean = !0;
    }
  }
  v("jspb.Map", P, void 0),
    (P.prototype.g = function () {
      if (this.arrClean) {
        if (this.b) {
          var t = this.a,
            o;
          for (o in t)
            if (Object.prototype.hasOwnProperty.call(t, o)) {
              var e = t[o].a;
              e && e.g();
            }
        }
      } else {
        for (
          this.c.length = 0, t = R(this), t.sort(), o = 0;
          o < t.length;
          o++
        ) {
          var r = this.a[t[o]];
          (e = r.a) && e.g(), this.c.push([r.key, r.value]);
        }
        this.arrClean = !0;
      }
      return this.c;
    }),
    (P.prototype.toArray = P.prototype.g),
    (P.prototype.Mc = function (t, o) {
      for (var e = this.g(), r = [], n = 0; n < e.length; n++) {
        var s = this.a[e[n][0].toString()];
        L(this, s);
        var l = s.a;
        l ? (u(o), r.push([s.key, o(t, l)])) : r.push([s.key, s.value]);
      }
      return r;
    }),
    (P.prototype.toObject = P.prototype.Mc),
    (P.fromObject = function (t, o, e) {
      o = new P([], o);
      for (var r = 0; r < t.length; r++) {
        var n = t[r][0],
          s = e(t[r][1]);
        o.set(n, s);
      }
      return o;
    });
  function J(t) {
    (this.a = 0), (this.b = t);
  }
  (J.prototype.next = function () {
    return this.a < this.b.length
      ? { done: !1, value: this.b[this.a++] }
      : { done: !0, value: void 0 };
  }),
    typeof Symbol != "undefined" &&
      (J.prototype[Symbol.iterator] = function () {
        return this;
      }),
    (P.prototype.Jb = function () {
      return R(this).length;
    }),
    (P.prototype.getLength = P.prototype.Jb),
    (P.prototype.clear = function () {
      (this.a = {}), (this.arrClean = !1);
    }),
    (P.prototype.clear = P.prototype.clear),
    (P.prototype.Cb = function (t) {
      t = t.toString();
      var o = this.a.hasOwnProperty(t);
      return delete this.a[t], (this.arrClean = !1), o;
    }),
    (P.prototype.del = P.prototype.Cb),
    (P.prototype.Eb = function () {
      var t = [],
        o = R(this);
      o.sort();
      for (var e = 0; e < o.length; e++) {
        var r = this.a[o[e]];
        t.push([r.key, r.value]);
      }
      return t;
    }),
    (P.prototype.getEntryList = P.prototype.Eb),
    (P.prototype.entries = function () {
      var t = [],
        o = R(this);
      o.sort();
      for (var e = 0; e < o.length; e++) {
        var r = this.a[o[e]];
        t.push([r.key, L(this, r)]);
      }
      return new J(t);
    }),
    (P.prototype.entries = P.prototype.entries),
    (P.prototype.keys = function () {
      var t = [],
        o = R(this);
      o.sort();
      for (var e = 0; e < o.length; e++) t.push(this.a[o[e]].key);
      return new J(t);
    }),
    (P.prototype.keys = P.prototype.keys),
    (P.prototype.values = function () {
      var t = [],
        o = R(this);
      o.sort();
      for (var e = 0; e < o.length; e++) t.push(L(this, this.a[o[e]]));
      return new J(t);
    }),
    (P.prototype.values = P.prototype.values),
    (P.prototype.forEach = function (t, o) {
      var e = R(this);
      e.sort();
      for (var r = 0; r < e.length; r++) {
        var n = this.a[e[r]];
        t.call(o, L(this, n), n.key, this);
      }
    }),
    (P.prototype.forEach = P.prototype.forEach),
    (P.prototype.set = function (t, o) {
      var e = new It(t);
      return (
        this.b ? ((e.a = o), (e.value = o.g())) : (e.value = o),
        (this.a[t.toString()] = e),
        (this.arrClean = !1),
        this
      );
    }),
    (P.prototype.set = P.prototype.set);
  function L(t, o) {
    return t.b ? (o.a || (o.a = new t.b(o.value)), o.a) : o.value;
  }
  (P.prototype.get = function (t) {
    if ((t = this.a[t.toString()])) return L(this, t);
  }),
    (P.prototype.get = P.prototype.get),
    (P.prototype.has = function (t) {
      return t.toString() in this.a;
    }),
    (P.prototype.has = P.prototype.has),
    (P.prototype.Jc = function (t, o, e, r, n) {
      var s = R(this);
      s.sort();
      for (var l = 0; l < s.length; l++) {
        var w = this.a[s[l]];
        o.Va(t),
          e.call(o, 1, w.key),
          this.b ? r.call(o, 2, L(this, w), n) : r.call(o, 2, w.value),
          o.Ya();
      }
    }),
    (P.prototype.serializeBinary = P.prototype.Jc),
    (P.deserializeBinary = function (t, o, e, r, n, s, l) {
      for (; o.oa() && !o.bb(); ) {
        var w = o.c;
        w == 1
          ? (s = e.call(o))
          : w == 2 &&
            (t.b
              ? (u(n), l || (l = new t.b()), r.call(o, l, n))
              : (l = r.call(o)));
      }
      u(s != null), u(l != null), t.set(s, l);
    });
  function R(t) {
    t = t.a;
    var o = [],
      e;
    for (e in t) Object.prototype.hasOwnProperty.call(t, e) && o.push(e);
    return o;
  }
  function It(t, o) {
    (this.key = t), (this.value = o), (this.a = void 0);
  }
  function Ct(t) {
    if (8192 >= t.length) return String.fromCharCode.apply(null, t);
    for (var o = "", e = 0; e < t.length; e += 8192)
      o += String.fromCharCode.apply(null, vo(t, e, e + 8192));
    return o;
  }
  var dt = {
      "\0": "\\0",
      "\b": "\\b",
      "\f": "\\f",
      "\n": "\\n",
      "\r": "\\r",
      "	": "\\t",
      "\v": "\\x0B",
      '"': '\\"',
      "\\": "\\\\",
      "<": "\\u003C",
    },
    nt = { "'": "\\'" },
    Ot = {},
    K = null;
  function Ut(t, o) {
    o === void 0 && (o = 0), Wt(), (o = Ot[o]);
    for (var e = [], r = 0; r < t.length; r += 3) {
      var n = t[r],
        s = r + 1 < t.length,
        l = s ? t[r + 1] : 0,
        w = r + 2 < t.length,
        x = w ? t[r + 2] : 0,
        V = n >> 2;
      (n = ((n & 3) << 4) | (l >> 4)),
        (l = ((l & 15) << 2) | (x >> 6)),
        (x &= 63),
        w || ((x = 64), s || (l = 64)),
        e.push(o[V], o[n], o[l] || "", o[x] || "");
    }
    return e.join("");
  }
  function Dt(t) {
    var o = t.length,
      e = (3 * o) / 4;
    e % 3
      ? (e = Math.floor(e))
      : "=.".indexOf(t[o - 1]) != -1 &&
        (e = "=.".indexOf(t[o - 2]) != -1 ? e - 2 : e - 1);
    var r = new Uint8Array(e),
      n = 0;
    return (
      Fo(t, function (s) {
        r[n++] = s;
      }),
      r.subarray(0, n)
    );
  }
  function Fo(t, o) {
    function e(x) {
      for (; r < t.length; ) {
        var V = t.charAt(r++),
          no = K[V];
        if (no != null) return no;
        if (!/^[\s\xa0]*$/.test(V))
          throw Error("Unknown base64 encoding at char: " + V);
      }
      return x;
    }
    Wt();
    for (var r = 0; ; ) {
      var n = e(-1),
        s = e(0),
        l = e(64),
        w = e(64);
      if (w === 64 && n === -1) break;
      o((n << 2) | (s >> 4)),
        l != 64 &&
          (o(((s << 4) & 240) | (l >> 2)), w != 64 && o(((l << 6) & 192) | w));
    }
  }
  function Wt() {
    if (!K) {
      K = {};
      for (
        var t =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(
              ""
            ),
          o = ["+/=", "+/", "-_=", "-_.", "-_"],
          e = 0;
        5 > e;
        e++
      ) {
        var r = t.concat(o[e].split(""));
        Ot[e] = r;
        for (var n = 0; n < r.length; n++) {
          var s = r[n];
          K[s] === void 0 && (K[s] = n);
        }
      }
    }
  }
  v("jspb.ConstBinaryMessage", function () {}, void 0),
    v("jspb.BinaryMessage", function () {}, void 0),
    v(
      "jspb.BinaryConstants.FieldType",
      {
        yb: -1,
        ee: 1,
        FLOAT: 2,
        ke: 3,
        te: 4,
        je: 5,
        xb: 6,
        wb: 7,
        BOOL: 8,
        re: 9,
        ie: 10,
        le: 11,
        ce: 12,
        se: 13,
        ge: 14,
        me: 15,
        ne: 16,
        oe: 17,
        pe: 18,
        he: 30,
        ve: 31,
      },
      void 0
    ),
    v(
      "jspb.BinaryConstants.WireType",
      { yb: -1, ue: 0, xb: 1, de: 2, qe: 3, fe: 4, wb: 5 },
      void 0
    ),
    v(
      "jspb.BinaryConstants.FieldTypeToWireType",
      function (t) {
        switch (t) {
          case 5:
          case 3:
          case 13:
          case 4:
          case 17:
          case 18:
          case 8:
          case 14:
          case 31:
            return 0;
          case 1:
          case 6:
          case 16:
          case 30:
            return 1;
          case 9:
          case 11:
          case 12:
            return 2;
          case 2:
          case 7:
          case 15:
            return 5;
          default:
            return -1;
        }
      },
      void 0
    ),
    v("jspb.BinaryConstants.INVALID_FIELD_NUMBER", -1, void 0),
    v("jspb.BinaryConstants.FLOAT32_EPS", 1401298464324817e-60, void 0),
    v("jspb.BinaryConstants.FLOAT32_MIN", 11754943508222875e-54, void 0),
    v("jspb.BinaryConstants.FLOAT32_MAX", 34028234663852886e22, void 0),
    v("jspb.BinaryConstants.FLOAT64_EPS", 5e-324, void 0),
    v("jspb.BinaryConstants.FLOAT64_MIN", 22250738585072014e-324, void 0),
    v("jspb.BinaryConstants.FLOAT64_MAX", 17976931348623157e292, void 0),
    v("jspb.BinaryConstants.TWO_TO_20", 1048576, void 0),
    v("jspb.BinaryConstants.TWO_TO_23", 8388608, void 0),
    v("jspb.BinaryConstants.TWO_TO_31", 2147483648, void 0),
    v("jspb.BinaryConstants.TWO_TO_32", 4294967296, void 0),
    v("jspb.BinaryConstants.TWO_TO_52", 4503599627370496, void 0),
    v("jspb.BinaryConstants.TWO_TO_63", 9223372036854776e3, void 0),
    v("jspb.BinaryConstants.TWO_TO_64", 18446744073709552e3, void 0),
    v("jspb.BinaryConstants.ZERO_HASH", "\0\0\0\0\0\0\0\0", void 0);
  var M = 0,
    b = 0;
  v(
    "jspb.utils.getSplit64Low",
    function () {
      return M;
    },
    void 0
  ),
    v(
      "jspb.utils.getSplit64High",
      function () {
        return b;
      },
      void 0
    );
  function vt(t) {
    var o = t >>> 0;
    (t = Math.floor((t - o) / 4294967296) >>> 0), (M = o), (b = t);
  }
  v("jspb.utils.splitUint64", vt, void 0);
  function X(t) {
    var o = 0 > t;
    t = Math.abs(t);
    var e = t >>> 0;
    (t = Math.floor((t - e) / 4294967296)),
      (t >>>= 0),
      o &&
        ((t = ~t >>> 0),
        (e = (~e >>> 0) + 1),
        4294967295 < e && ((e = 0), t++, 4294967295 < t && (t = 0))),
      (M = e),
      (b = t);
  }
  v("jspb.utils.splitInt64", X, void 0);
  function Rt(t) {
    var o = 0 > t;
    (t = 2 * Math.abs(t)), vt(t), (t = M);
    var e = b;
    o &&
      (t == 0
        ? e == 0
          ? (e = t = 4294967295)
          : (e--, (t = 4294967295))
        : t--),
      (M = t),
      (b = e);
  }
  v("jspb.utils.splitZigzag64", Rt, void 0);
  function Tt(t) {
    var o = 0 > t ? 1 : 0;
    if (((t = o ? -t : t), t === 0))
      0 < 1 / t ? (M = b = 0) : ((b = 0), (M = 2147483648));
    else if (isNaN(t)) (b = 0), (M = 2147483647);
    else if (34028234663852886e22 < t)
      (b = 0), (M = ((o << 31) | 2139095040) >>> 0);
    else if (11754943508222875e-54 > t)
      (t = Math.round(t / Math.pow(2, -149))),
        (b = 0),
        (M = ((o << 31) | t) >>> 0);
    else {
      var e = Math.floor(Math.log(t) / Math.LN2);
      (t *= Math.pow(2, -e)),
        (t = Math.round(8388608 * t)),
        16777216 <= t && ++e,
        (b = 0),
        (M = ((o << 31) | ((e + 127) << 23) | (t & 8388607)) >>> 0);
    }
  }
  v("jspb.utils.splitFloat32", Tt, void 0);
  function Nt(t) {
    var o = 0 > t ? 1 : 0;
    if (((t = o ? -t : t), t === 0)) (b = 0 < 1 / t ? 0 : 2147483648), (M = 0);
    else if (isNaN(t)) (b = 2147483647), (M = 4294967295);
    else if (17976931348623157e292 < t)
      (b = ((o << 31) | 2146435072) >>> 0), (M = 0);
    else if (22250738585072014e-324 > t)
      (t /= Math.pow(2, -1074)),
        (b = ((o << 31) | (t / 4294967296)) >>> 0),
        (M = t >>> 0);
    else {
      var e = t,
        r = 0;
      if (2 <= e) for (; 2 <= e && 1023 > r; ) r++, (e /= 2);
      else for (; 1 > e && -1022 < r; ) (e *= 2), r--;
      (t *= Math.pow(2, -r)),
        (b =
          ((o << 31) | ((r + 1023) << 20) | ((1048576 * t) & 1048575)) >>> 0),
        (M = (4503599627370496 * t) >>> 0);
    }
  }
  v("jspb.utils.splitFloat64", Nt, void 0);
  function T(t) {
    var o = t.charCodeAt(4),
      e = t.charCodeAt(5),
      r = t.charCodeAt(6),
      n = t.charCodeAt(7);
    (M =
      (t.charCodeAt(0) +
        (t.charCodeAt(1) << 8) +
        (t.charCodeAt(2) << 16) +
        (t.charCodeAt(3) << 24)) >>>
      0),
      (b = (o + (e << 8) + (r << 16) + (n << 24)) >>> 0);
  }
  v("jspb.utils.splitHash64", T, void 0);
  function H(t, o) {
    return 4294967296 * o + (t >>> 0);
  }
  v("jspb.utils.joinUint64", H, void 0);
  function $(t, o) {
    var e = o & 2147483648;
    return (
      e &&
        ((t = (~t + 1) >>> 0), (o = ~o >>> 0), t == 0 && (o = (o + 1) >>> 0)),
      (t = H(t, o)),
      e ? -t : t
    );
  }
  v("jspb.utils.joinInt64", $, void 0);
  function pt(t, o, e) {
    var r = o >> 31;
    return e((t << 1) ^ r, ((o << 1) | (t >>> 31)) ^ r);
  }
  v("jspb.utils.toZigzag64", pt, void 0);
  function Vt(t, o) {
    return st(t, o, $);
  }
  v("jspb.utils.joinZigzag64", Vt, void 0);
  function st(t, o, e) {
    var r = -(t & 1);
    return e(((t >>> 1) | (o << 31)) ^ r, (o >>> 1) ^ r);
  }
  v("jspb.utils.fromZigzag64", st, void 0);
  function zt(t) {
    var o = 2 * (t >> 31) + 1,
      e = (t >>> 23) & 255;
    return (
      (t &= 8388607),
      e == 255
        ? t
          ? NaN
          : (1 / 0) * o
        : e == 0
        ? o * Math.pow(2, -149) * t
        : o * Math.pow(2, e - 150) * (t + Math.pow(2, 23))
    );
  }
  v("jspb.utils.joinFloat32", zt, void 0);
  function Lt(t, o) {
    var e = 2 * (o >> 31) + 1,
      r = (o >>> 20) & 2047;
    return (
      (t = 4294967296 * (o & 1048575) + t),
      r == 2047
        ? t
          ? NaN
          : (1 / 0) * e
        : r == 0
        ? e * Math.pow(2, -1074) * t
        : e * Math.pow(2, r - 1075) * (t + 4503599627370496)
    );
  }
  v("jspb.utils.joinFloat64", Lt, void 0);
  function at(t, o) {
    return String.fromCharCode(
      (t >>> 0) & 255,
      (t >>> 8) & 255,
      (t >>> 16) & 255,
      (t >>> 24) & 255,
      (o >>> 0) & 255,
      (o >>> 8) & 255,
      (o >>> 16) & 255,
      (o >>> 24) & 255
    );
  }
  v("jspb.utils.joinHash64", at, void 0),
    v("jspb.utils.DIGITS", "0123456789abcdef".split(""), void 0);
  function Y(t, o) {
    function e(n, s) {
      return (n = n ? String(n) : ""), s ? "0000000".slice(n.length) + n : n;
    }
    if (2097151 >= o) return "" + H(t, o);
    var r = (((t >>> 24) | (o << 8)) >>> 0) & 16777215;
    return (
      (o = (o >> 16) & 65535),
      (t = (t & 16777215) + 6777216 * r + 6710656 * o),
      (r += 8147497 * o),
      (o *= 2),
      1e7 <= t && ((r += Math.floor(t / 1e7)), (t %= 1e7)),
      1e7 <= r && ((o += Math.floor(r / 1e7)), (r %= 1e7)),
      e(o, 0) + e(r, o) + e(t, 1)
    );
  }
  v("jspb.utils.joinUnsignedDecimalString", Y, void 0);
  function q(t, o) {
    var e = o & 2147483648;
    return (
      e && ((t = (~t + 1) >>> 0), (o = (~o + (t == 0 ? 1 : 0)) >>> 0)),
      (t = Y(t, o)),
      e ? "-" + t : t
    );
  }
  v("jspb.utils.joinSignedDecimalString", q, void 0);
  function Ht(t, o) {
    T(t), (t = M);
    var e = b;
    return o ? q(t, e) : Y(t, e);
  }
  v("jspb.utils.hash64ToDecimalString", Ht, void 0),
    v(
      "jspb.utils.hash64ArrayToDecimalStrings",
      function (t, o) {
        for (var e = Array(t.length), r = 0; r < t.length; r++)
          e[r] = Ht(t[r], o);
        return e;
      },
      void 0
    );
  function Q(t) {
    function o(l, w) {
      for (var x = 0; 8 > x && (l !== 1 || 0 < w); x++)
        (w = l * n[x] + w), (n[x] = w & 255), (w >>>= 8);
    }
    function e() {
      for (var l = 0; 8 > l; l++) n[l] = ~n[l] & 255;
    }
    u(0 < t.length);
    var r = !1;
    t[0] === "-" && ((r = !0), (t = t.slice(1)));
    for (var n = [0, 0, 0, 0, 0, 0, 0, 0], s = 0; s < t.length; s++)
      o(10, t.charCodeAt(s) - 48);
    return r && (e(), o(1, 1)), Ct(n);
  }
  v("jspb.utils.decimalStringToHash64", Q, void 0),
    v(
      "jspb.utils.splitDecimalString",
      function (t) {
        T(Q(t));
      },
      void 0
    );
  function mt(t) {
    return String.fromCharCode(10 > t ? 48 + t : 87 + t);
  }
  function Gt(t) {
    return 97 <= t ? t - 97 + 10 : t - 48;
  }
  v(
    "jspb.utils.hash64ToHexString",
    function (t) {
      var o = Array(18);
      (o[0] = "0"), (o[1] = "x");
      for (var e = 0; 8 > e; e++) {
        var r = t.charCodeAt(7 - e);
        (o[2 * e + 2] = mt(r >> 4)), (o[2 * e + 3] = mt(r & 15));
      }
      return o.join("");
    },
    void 0
  ),
    v(
      "jspb.utils.hexStringToHash64",
      function (t) {
        (t = t.toLowerCase()),
          u(t.length == 18),
          u(t[0] == "0"),
          u(t[1] == "x");
        for (var o = "", e = 0; 8 > e; e++)
          o =
            String.fromCharCode(
              16 * Gt(t.charCodeAt(2 * e + 2)) + Gt(t.charCodeAt(2 * e + 3))
            ) + o;
        return o;
      },
      void 0
    ),
    v(
      "jspb.utils.hash64ToNumber",
      function (t, o) {
        T(t), (t = M);
        var e = b;
        return o ? $(t, e) : H(t, e);
      },
      void 0
    ),
    v(
      "jspb.utils.numberToHash64",
      function (t) {
        return X(t), at(M, b);
      },
      void 0
    ),
    v(
      "jspb.utils.countVarints",
      function (t, o, e) {
        for (var r = 0, n = o; n < e; n++) r += t[n] >> 7;
        return e - o - r;
      },
      void 0
    ),
    v(
      "jspb.utils.countVarintFields",
      function (t, o, e, r) {
        var n = 0;
        if (((r *= 8), 128 > r))
          for (; o < e && t[o++] == r; )
            for (n++; ; ) {
              var s = t[o++];
              if (!(s & 128)) break;
            }
        else
          for (; o < e; ) {
            for (s = r; 128 < s; ) {
              if (t[o] != ((s & 127) | 128)) return n;
              o++, (s >>= 7);
            }
            if (t[o++] != s) break;
            for (n++; (s = t[o++]), (s & 128) != 0; );
          }
        return n;
      },
      void 0
    );
  function _t(t, o, e, r, n) {
    var s = 0;
    if (128 > r) for (; o < e && t[o++] == r; ) s++, (o += n);
    else
      for (; o < e; ) {
        for (var l = r; 128 < l; ) {
          if (t[o++] != ((l & 127) | 128)) return s;
          l >>= 7;
        }
        if (t[o++] != l) break;
        s++, (o += n);
      }
    return s;
  }
  v(
    "jspb.utils.countFixed32Fields",
    function (t, o, e, r) {
      return _t(t, o, e, 8 * r + 5, 4);
    },
    void 0
  ),
    v(
      "jspb.utils.countFixed64Fields",
      function (t, o, e, r) {
        return _t(t, o, e, 8 * r + 1, 8);
      },
      void 0
    ),
    v(
      "jspb.utils.countDelimitedFields",
      function (t, o, e, r) {
        var n = 0;
        for (r = 8 * r + 2; o < e; ) {
          for (var s = r; 128 < s; ) {
            if (t[o++] != ((s & 127) | 128)) return n;
            s >>= 7;
          }
          if (t[o++] != s) break;
          n++;
          for (
            var l = 0, w = 1;
            (s = t[o++]), (l += (s & 127) * w), (w *= 128), (s & 128) != 0;

          );
          o += l;
        }
        return n;
      },
      void 0
    ),
    v(
      "jspb.utils.debugBytesToTextFormat",
      function (t) {
        var o = '"';
        if (t) {
          t = ut(t);
          for (var e = 0; e < t.length; e++)
            (o += "\\x"), 16 > t[e] && (o += "0"), (o += t[e].toString(16));
        }
        return o + '"';
      },
      void 0
    ),
    v(
      "jspb.utils.debugScalarToTextFormat",
      function (t) {
        if (typeof t == "string") {
          t = String(t);
          for (var o = ['"'], e = 0; e < t.length; e++) {
            var r = t.charAt(e),
              n = r.charCodeAt(0),
              s = e + 1,
              l;
            (l = dt[r]) ||
              ((31 < n && 127 > n) ||
                ((n = r),
                n in nt
                  ? (r = nt[n])
                  : n in dt
                  ? (r = nt[n] = dt[n])
                  : ((l = n.charCodeAt(0)),
                    31 < l && 127 > l
                      ? (r = n)
                      : (256 > l
                          ? ((r = "\\x"), (16 > l || 256 < l) && (r += "0"))
                          : ((r = "\\u"), 4096 > l && (r += "0")),
                        (r += l.toString(16).toUpperCase())),
                    (r = nt[n] = r))),
              (l = r)),
              (o[s] = l);
          }
          o.push('"'), (t = o.join(""));
        } else t = t.toString();
        return t;
      },
      void 0
    ),
    v(
      "jspb.utils.stringToByteArray",
      function (t) {
        for (var o = new Uint8Array(t.length), e = 0; e < t.length; e++) {
          var r = t.charCodeAt(e);
          if (255 < r)
            throw Error(
              "Conversion error: string contains codepoint outside of byte range"
            );
          o[e] = r;
        }
        return o;
      },
      void 0
    );
  function ut(t) {
    return t.constructor === Uint8Array
      ? t
      : t.constructor === ArrayBuffer
      ? new Uint8Array(t)
      : t.constructor === Array
      ? new Uint8Array(t)
      : t.constructor === String
      ? Dt(t)
      : t instanceof Uint8Array
      ? new Uint8Array(t.buffer, t.byteOffset, t.byteLength)
      : (B("Type not convertible to Uint8Array."), new Uint8Array(0));
  }
  v("jspb.utils.byteSourceToUint8Array", ut, void 0);
  function a(t, o, e) {
    (this.b = null),
      (this.a = this.c = this.h = 0),
      (this.v = !1),
      t && this.H(t, o, e);
  }
  v("jspb.BinaryDecoder", a, void 0);
  var tt = [];
  a.getInstanceCacheLength = function () {
    return tt.length;
  };
  function ht(t, o, e) {
    if (tt.length) {
      var r = tt.pop();
      return t && r.H(t, o, e), r;
    }
    return new a(t, o, e);
  }
  (a.alloc = ht),
    (a.prototype.Ca = function () {
      this.clear(), 100 > tt.length && tt.push(this);
    }),
    (a.prototype.free = a.prototype.Ca),
    (a.prototype.clone = function () {
      return ht(this.b, this.h, this.c - this.h);
    }),
    (a.prototype.clone = a.prototype.clone),
    (a.prototype.clear = function () {
      (this.b = null), (this.a = this.c = this.h = 0), (this.v = !1);
    }),
    (a.prototype.clear = a.prototype.clear),
    (a.prototype.Y = function () {
      return this.b;
    }),
    (a.prototype.getBuffer = a.prototype.Y),
    (a.prototype.H = function (t, o, e) {
      (this.b = ut(t)),
        (this.h = o !== void 0 ? o : 0),
        (this.c = e !== void 0 ? this.h + e : this.b.length),
        (this.a = this.h);
    }),
    (a.prototype.setBlock = a.prototype.H),
    (a.prototype.Db = function () {
      return this.c;
    }),
    (a.prototype.getEnd = a.prototype.Db),
    (a.prototype.setEnd = function (t) {
      this.c = t;
    }),
    (a.prototype.setEnd = a.prototype.setEnd),
    (a.prototype.reset = function () {
      this.a = this.h;
    }),
    (a.prototype.reset = a.prototype.reset),
    (a.prototype.B = function () {
      return this.a;
    }),
    (a.prototype.getCursor = a.prototype.B),
    (a.prototype.Ma = function (t) {
      this.a = t;
    }),
    (a.prototype.setCursor = a.prototype.Ma),
    (a.prototype.advance = function (t) {
      (this.a += t), u(this.a <= this.c);
    }),
    (a.prototype.advance = a.prototype.advance),
    (a.prototype.ya = function () {
      return this.a == this.c;
    }),
    (a.prototype.atEnd = a.prototype.ya),
    (a.prototype.Qb = function () {
      return this.a > this.c;
    }),
    (a.prototype.pastEnd = a.prototype.Qb),
    (a.prototype.getError = function () {
      return this.v || 0 > this.a || this.a > this.c;
    }),
    (a.prototype.getError = a.prototype.getError),
    (a.prototype.w = function (t) {
      for (var o = 128, e = 0, r = 0, n = 0; 4 > n && 128 <= o; n++)
        (o = this.b[this.a++]), (e |= (o & 127) << (7 * n));
      if (
        (128 <= o &&
          ((o = this.b[this.a++]),
          (e |= (o & 127) << 28),
          (r |= (o & 127) >> 4)),
        128 <= o)
      )
        for (n = 0; 5 > n && 128 <= o; n++)
          (o = this.b[this.a++]), (r |= (o & 127) << (7 * n + 3));
      if (128 > o) return t(e >>> 0, r >>> 0);
      B("Failed to read varint, encoding is invalid."), (this.v = !0);
    }),
    (a.prototype.readSplitVarint64 = a.prototype.w),
    (a.prototype.ea = function (t) {
      return this.w(function (o, e) {
        return st(o, e, t);
      });
    }),
    (a.prototype.readSplitZigzagVarint64 = a.prototype.ea),
    (a.prototype.ta = function (t) {
      var o = this.b,
        e = this.a;
      this.a += 8;
      for (var r = 0, n = 0, s = e + 7; s >= e; s--)
        (r = (r << 8) | o[s]), (n = (n << 8) | o[s + 4]);
      return t(r, n);
    }),
    (a.prototype.readSplitFixed64 = a.prototype.ta),
    (a.prototype.kb = function () {
      for (; this.b[this.a] & 128; ) this.a++;
      this.a++;
    }),
    (a.prototype.skipVarint = a.prototype.kb),
    (a.prototype.mb = function (t) {
      for (; 128 < t; ) this.a--, (t >>>= 7);
      this.a--;
    }),
    (a.prototype.unskipVarint = a.prototype.mb),
    (a.prototype.o = function () {
      var t = this.b,
        o = t[this.a],
        e = o & 127;
      return 128 > o
        ? ((this.a += 1), u(this.a <= this.c), e)
        : ((o = t[this.a + 1]),
          (e |= (o & 127) << 7),
          128 > o
            ? ((this.a += 2), u(this.a <= this.c), e)
            : ((o = t[this.a + 2]),
              (e |= (o & 127) << 14),
              128 > o
                ? ((this.a += 3), u(this.a <= this.c), e)
                : ((o = t[this.a + 3]),
                  (e |= (o & 127) << 21),
                  128 > o
                    ? ((this.a += 4), u(this.a <= this.c), e)
                    : ((o = t[this.a + 4]),
                      (e |= (o & 15) << 28),
                      128 > o
                        ? ((this.a += 5), u(this.a <= this.c), e >>> 0)
                        : ((this.a += 5),
                          128 <= t[this.a++] &&
                            128 <= t[this.a++] &&
                            128 <= t[this.a++] &&
                            128 <= t[this.a++] &&
                            128 <= t[this.a++] &&
                            u(!1),
                          u(this.a <= this.c),
                          e)))));
    }),
    (a.prototype.readUnsignedVarint32 = a.prototype.o),
    (a.prototype.da = function () {
      return ~~this.o();
    }),
    (a.prototype.readSignedVarint32 = a.prototype.da),
    (a.prototype.O = function () {
      return this.o().toString();
    }),
    (a.prototype.Ea = function () {
      return this.da().toString();
    }),
    (a.prototype.readSignedVarint32String = a.prototype.Ea),
    (a.prototype.Ia = function () {
      var t = this.o();
      return (t >>> 1) ^ -(t & 1);
    }),
    (a.prototype.readZigzagVarint32 = a.prototype.Ia),
    (a.prototype.Ga = function () {
      return this.w(H);
    }),
    (a.prototype.readUnsignedVarint64 = a.prototype.Ga),
    (a.prototype.Ha = function () {
      return this.w(Y);
    }),
    (a.prototype.readUnsignedVarint64String = a.prototype.Ha),
    (a.prototype.sa = function () {
      return this.w($);
    }),
    (a.prototype.readSignedVarint64 = a.prototype.sa),
    (a.prototype.Fa = function () {
      return this.w(q);
    }),
    (a.prototype.readSignedVarint64String = a.prototype.Fa),
    (a.prototype.Ja = function () {
      return this.w(Vt);
    }),
    (a.prototype.readZigzagVarint64 = a.prototype.Ja),
    (a.prototype.fb = function () {
      return this.ea(at);
    }),
    (a.prototype.readZigzagVarintHash64 = a.prototype.fb),
    (a.prototype.Ka = function () {
      return this.ea(q);
    }),
    (a.prototype.readZigzagVarint64String = a.prototype.Ka),
    (a.prototype.Gc = function () {
      var t = this.b[this.a];
      return (this.a += 1), u(this.a <= this.c), t;
    }),
    (a.prototype.readUint8 = a.prototype.Gc),
    (a.prototype.Ec = function () {
      var t = this.b[this.a],
        o = this.b[this.a + 1];
      return (this.a += 2), u(this.a <= this.c), (t << 0) | (o << 8);
    }),
    (a.prototype.readUint16 = a.prototype.Ec),
    (a.prototype.m = function () {
      var t = this.b[this.a],
        o = this.b[this.a + 1],
        e = this.b[this.a + 2],
        r = this.b[this.a + 3];
      return (
        (this.a += 4),
        u(this.a <= this.c),
        ((t << 0) | (o << 8) | (e << 16) | (r << 24)) >>> 0
      );
    }),
    (a.prototype.readUint32 = a.prototype.m),
    (a.prototype.ga = function () {
      var t = this.m(),
        o = this.m();
      return H(t, o);
    }),
    (a.prototype.readUint64 = a.prototype.ga),
    (a.prototype.ha = function () {
      var t = this.m(),
        o = this.m();
      return Y(t, o);
    }),
    (a.prototype.readUint64String = a.prototype.ha),
    (a.prototype.Xb = function () {
      var t = this.b[this.a];
      return (this.a += 1), u(this.a <= this.c), (t << 24) >> 24;
    }),
    (a.prototype.readInt8 = a.prototype.Xb),
    (a.prototype.Vb = function () {
      var t = this.b[this.a],
        o = this.b[this.a + 1];
      return (
        (this.a += 2), u(this.a <= this.c), (((t << 0) | (o << 8)) << 16) >> 16
      );
    }),
    (a.prototype.readInt16 = a.prototype.Vb),
    (a.prototype.P = function () {
      var t = this.b[this.a],
        o = this.b[this.a + 1],
        e = this.b[this.a + 2],
        r = this.b[this.a + 3];
      return (
        (this.a += 4),
        u(this.a <= this.c),
        (t << 0) | (o << 8) | (e << 16) | (r << 24)
      );
    }),
    (a.prototype.readInt32 = a.prototype.P),
    (a.prototype.ba = function () {
      var t = this.m(),
        o = this.m();
      return $(t, o);
    }),
    (a.prototype.readInt64 = a.prototype.ba),
    (a.prototype.ca = function () {
      var t = this.m(),
        o = this.m();
      return q(t, o);
    }),
    (a.prototype.readInt64String = a.prototype.ca),
    (a.prototype.aa = function () {
      var t = this.m();
      return zt(t);
    }),
    (a.prototype.readFloat = a.prototype.aa),
    (a.prototype.Z = function () {
      var t = this.m(),
        o = this.m();
      return Lt(t, o);
    }),
    (a.prototype.readDouble = a.prototype.Z),
    (a.prototype.pa = function () {
      return !!this.b[this.a++];
    }),
    (a.prototype.readBool = a.prototype.pa),
    (a.prototype.ra = function () {
      return this.da();
    }),
    (a.prototype.readEnum = a.prototype.ra),
    (a.prototype.fa = function (t) {
      var o = this.b,
        e = this.a;
      t = e + t;
      for (var r = [], n = ""; e < t; ) {
        var s = o[e++];
        if (128 > s) r.push(s);
        else {
          if (192 > s) continue;
          if (224 > s) {
            var l = o[e++];
            r.push(((s & 31) << 6) | (l & 63));
          } else if (240 > s) {
            l = o[e++];
            var w = o[e++];
            r.push(((s & 15) << 12) | ((l & 63) << 6) | (w & 63));
          } else if (248 > s) {
            (l = o[e++]), (w = o[e++]);
            var x = o[e++];
            (s =
              ((s & 7) << 18) | ((l & 63) << 12) | ((w & 63) << 6) | (x & 63)),
              (s -= 65536),
              r.push(((s >> 10) & 1023) + 55296, (s & 1023) + 56320);
          }
        }
        8192 <= r.length &&
          ((n += String.fromCharCode.apply(null, r)), (r.length = 0));
      }
      return (n += Ct(r)), (this.a = e), n;
    }),
    (a.prototype.readString = a.prototype.fa),
    (a.prototype.Dc = function () {
      var t = this.o();
      return this.fa(t);
    }),
    (a.prototype.readStringWithLength = a.prototype.Dc),
    (a.prototype.qa = function (t) {
      if (0 > t || this.a + t > this.b.length)
        return (this.v = !0), B("Invalid byte length!"), new Uint8Array(0);
      var o = this.b.subarray(this.a, this.a + t);
      return (this.a += t), u(this.a <= this.c), o;
    }),
    (a.prototype.readBytes = a.prototype.qa),
    (a.prototype.ia = function () {
      return this.w(at);
    }),
    (a.prototype.readVarintHash64 = a.prototype.ia),
    (a.prototype.$ = function () {
      var t = this.b,
        o = this.a,
        e = t[o],
        r = t[o + 1],
        n = t[o + 2],
        s = t[o + 3],
        l = t[o + 4],
        w = t[o + 5],
        x = t[o + 6];
      return (
        (t = t[o + 7]),
        (this.a += 8),
        String.fromCharCode(e, r, n, s, l, w, x, t)
      );
    }),
    (a.prototype.readFixedHash64 = a.prototype.$);
  function p(t, o, e) {
    (this.a = ht(t, o, e)),
      (this.O = this.a.B()),
      (this.b = this.c = -1),
      (this.h = !1),
      (this.v = null);
  }
  v("jspb.BinaryReader", p, void 0);
  var m = [];
  (p.clearInstanceCache = function () {
    m = [];
  }),
    (p.getInstanceCacheLength = function () {
      return m.length;
    });
  function Zt(t, o, e) {
    if (m.length) {
      var r = m.pop();
      return t && r.a.H(t, o, e), r;
    }
    return new p(t, o, e);
  }
  (p.alloc = Zt),
    (p.prototype.zb = Zt),
    (p.prototype.alloc = p.prototype.zb),
    (p.prototype.Ca = function () {
      this.a.clear(),
        (this.b = this.c = -1),
        (this.h = !1),
        (this.v = null),
        100 > m.length && m.push(this);
    }),
    (p.prototype.free = p.prototype.Ca),
    (p.prototype.Fb = function () {
      return this.O;
    }),
    (p.prototype.getFieldCursor = p.prototype.Fb),
    (p.prototype.B = function () {
      return this.a.B();
    }),
    (p.prototype.getCursor = p.prototype.B),
    (p.prototype.Y = function () {
      return this.a.Y();
    }),
    (p.prototype.getBuffer = p.prototype.Y),
    (p.prototype.Hb = function () {
      return this.c;
    }),
    (p.prototype.getFieldNumber = p.prototype.Hb),
    (p.prototype.Lb = function () {
      return this.b;
    }),
    (p.prototype.getWireType = p.prototype.Lb),
    (p.prototype.Mb = function () {
      return this.b == 2;
    }),
    (p.prototype.isDelimited = p.prototype.Mb),
    (p.prototype.bb = function () {
      return this.b == 4;
    }),
    (p.prototype.isEndGroup = p.prototype.bb),
    (p.prototype.getError = function () {
      return this.h || this.a.getError();
    }),
    (p.prototype.getError = p.prototype.getError),
    (p.prototype.H = function (t, o, e) {
      this.a.H(t, o, e), (this.b = this.c = -1);
    }),
    (p.prototype.setBlock = p.prototype.H),
    (p.prototype.reset = function () {
      this.a.reset(), (this.b = this.c = -1);
    }),
    (p.prototype.reset = p.prototype.reset),
    (p.prototype.advance = function (t) {
      this.a.advance(t);
    }),
    (p.prototype.advance = p.prototype.advance),
    (p.prototype.oa = function () {
      if (this.a.ya()) return !1;
      if (this.getError()) return B("Decoder hit an error"), !1;
      this.O = this.a.B();
      var t = this.a.o(),
        o = t >>> 3;
      return (
        (t &= 7),
        t != 0 && t != 5 && t != 1 && t != 2 && t != 3 && t != 4
          ? (B("Invalid wire type: %s (at position %s)", t, this.O),
            (this.h = !0),
            !1)
          : ((this.c = o), (this.b = t), !0)
      );
    }),
    (p.prototype.nextField = p.prototype.oa),
    (p.prototype.Oa = function () {
      this.a.mb((this.c << 3) | this.b);
    }),
    (p.prototype.unskipHeader = p.prototype.Oa),
    (p.prototype.Lc = function () {
      var t = this.c;
      for (this.Oa(); this.oa() && this.c == t; ) this.C();
      this.a.ya() || this.Oa();
    }),
    (p.prototype.skipMatchingFields = p.prototype.Lc),
    (p.prototype.lb = function () {
      this.b != 0
        ? (B("Invalid wire type for skipVarintField"), this.C())
        : this.a.kb();
    }),
    (p.prototype.skipVarintField = p.prototype.lb),
    (p.prototype.gb = function () {
      if (this.b != 2) B("Invalid wire type for skipDelimitedField"), this.C();
      else {
        var t = this.a.o();
        this.a.advance(t);
      }
    }),
    (p.prototype.skipDelimitedField = p.prototype.gb),
    (p.prototype.hb = function () {
      this.b != 5
        ? (B("Invalid wire type for skipFixed32Field"), this.C())
        : this.a.advance(4);
    }),
    (p.prototype.skipFixed32Field = p.prototype.hb),
    (p.prototype.ib = function () {
      this.b != 1
        ? (B("Invalid wire type for skipFixed64Field"), this.C())
        : this.a.advance(8);
    }),
    (p.prototype.skipFixed64Field = p.prototype.ib),
    (p.prototype.jb = function () {
      var t = this.c;
      do {
        if (!this.oa()) {
          B("Unmatched start-group tag: stream EOF"), (this.h = !0);
          break;
        }
        if (this.b == 4) {
          this.c != t && (B("Unmatched end-group tag"), (this.h = !0));
          break;
        }
        this.C();
      } while (1);
    }),
    (p.prototype.skipGroup = p.prototype.jb),
    (p.prototype.C = function () {
      switch (this.b) {
        case 0:
          this.lb();
          break;
        case 1:
          this.ib();
          break;
        case 2:
          this.gb();
          break;
        case 5:
          this.hb();
          break;
        case 3:
          this.jb();
          break;
        default:
          B("Invalid wire encoding for field.");
      }
    }),
    (p.prototype.skipField = p.prototype.C),
    (p.prototype.Hc = function (t, o) {
      this.v === null && (this.v = {}), u(!this.v[t]), (this.v[t] = o);
    }),
    (p.prototype.registerReadCallback = p.prototype.Hc),
    (p.prototype.Ic = function (t) {
      return u(this.v !== null), (t = this.v[t]), u(t), t(this);
    }),
    (p.prototype.runReadCallback = p.prototype.Ic),
    (p.prototype.Yb = function (t, o) {
      u(this.b == 2);
      var e = this.a.c,
        r = this.a.o();
      (r = this.a.B() + r),
        this.a.setEnd(r),
        o(t, this),
        this.a.Ma(r),
        this.a.setEnd(e);
    }),
    (p.prototype.readMessage = p.prototype.Yb),
    (p.prototype.Ub = function (t, o, e) {
      u(this.b == 3),
        u(this.c == t),
        e(o, this),
        this.h ||
          this.b == 4 ||
          (B("Group submessage did not end with an END_GROUP tag"),
          (this.h = !0));
    }),
    (p.prototype.readGroup = p.prototype.Ub),
    (p.prototype.Gb = function () {
      u(this.b == 2);
      var t = this.a.o(),
        o = this.a.B(),
        e = o + t;
      return (t = ht(this.a.Y(), o, t)), this.a.Ma(e), t;
    }),
    (p.prototype.getFieldDecoder = p.prototype.Gb),
    (p.prototype.P = function () {
      return u(this.b == 0), this.a.da();
    }),
    (p.prototype.readInt32 = p.prototype.P),
    (p.prototype.Wb = function () {
      return u(this.b == 0), this.a.Ea();
    }),
    (p.prototype.readInt32String = p.prototype.Wb),
    (p.prototype.ba = function () {
      return u(this.b == 0), this.a.sa();
    }),
    (p.prototype.readInt64 = p.prototype.ba),
    (p.prototype.ca = function () {
      return u(this.b == 0), this.a.Fa();
    }),
    (p.prototype.readInt64String = p.prototype.ca),
    (p.prototype.m = function () {
      return u(this.b == 0), this.a.o();
    }),
    (p.prototype.readUint32 = p.prototype.m),
    (p.prototype.Fc = function () {
      return u(this.b == 0), this.a.O();
    }),
    (p.prototype.readUint32String = p.prototype.Fc),
    (p.prototype.ga = function () {
      return u(this.b == 0), this.a.Ga();
    }),
    (p.prototype.readUint64 = p.prototype.ga),
    (p.prototype.ha = function () {
      return u(this.b == 0), this.a.Ha();
    }),
    (p.prototype.readUint64String = p.prototype.ha),
    (p.prototype.zc = function () {
      return u(this.b == 0), this.a.Ia();
    }),
    (p.prototype.readSint32 = p.prototype.zc),
    (p.prototype.Ac = function () {
      return u(this.b == 0), this.a.Ja();
    }),
    (p.prototype.readSint64 = p.prototype.Ac),
    (p.prototype.Bc = function () {
      return u(this.b == 0), this.a.Ka();
    }),
    (p.prototype.readSint64String = p.prototype.Bc),
    (p.prototype.Rb = function () {
      return u(this.b == 5), this.a.m();
    }),
    (p.prototype.readFixed32 = p.prototype.Rb),
    (p.prototype.Sb = function () {
      return u(this.b == 1), this.a.ga();
    }),
    (p.prototype.readFixed64 = p.prototype.Sb),
    (p.prototype.Tb = function () {
      return u(this.b == 1), this.a.ha();
    }),
    (p.prototype.readFixed64String = p.prototype.Tb),
    (p.prototype.vc = function () {
      return u(this.b == 5), this.a.P();
    }),
    (p.prototype.readSfixed32 = p.prototype.vc),
    (p.prototype.wc = function () {
      return u(this.b == 5), this.a.P().toString();
    }),
    (p.prototype.readSfixed32String = p.prototype.wc),
    (p.prototype.xc = function () {
      return u(this.b == 1), this.a.ba();
    }),
    (p.prototype.readSfixed64 = p.prototype.xc),
    (p.prototype.yc = function () {
      return u(this.b == 1), this.a.ca();
    }),
    (p.prototype.readSfixed64String = p.prototype.yc),
    (p.prototype.aa = function () {
      return u(this.b == 5), this.a.aa();
    }),
    (p.prototype.readFloat = p.prototype.aa),
    (p.prototype.Z = function () {
      return u(this.b == 1), this.a.Z();
    }),
    (p.prototype.readDouble = p.prototype.Z),
    (p.prototype.pa = function () {
      return u(this.b == 0), !!this.a.o();
    }),
    (p.prototype.readBool = p.prototype.pa),
    (p.prototype.ra = function () {
      return u(this.b == 0), this.a.sa();
    }),
    (p.prototype.readEnum = p.prototype.ra),
    (p.prototype.fa = function () {
      u(this.b == 2);
      var t = this.a.o();
      return this.a.fa(t);
    }),
    (p.prototype.readString = p.prototype.fa),
    (p.prototype.qa = function () {
      u(this.b == 2);
      var t = this.a.o();
      return this.a.qa(t);
    }),
    (p.prototype.readBytes = p.prototype.qa),
    (p.prototype.ia = function () {
      return u(this.b == 0), this.a.ia();
    }),
    (p.prototype.readVarintHash64 = p.prototype.ia),
    (p.prototype.Cc = function () {
      return u(this.b == 0), this.a.fb();
    }),
    (p.prototype.readSintHash64 = p.prototype.Cc),
    (p.prototype.w = function (t) {
      return u(this.b == 0), this.a.w(t);
    }),
    (p.prototype.readSplitVarint64 = p.prototype.w),
    (p.prototype.ea = function (t) {
      return (
        u(this.b == 0),
        this.a.w(function (o, e) {
          return st(o, e, t);
        })
      );
    }),
    (p.prototype.readSplitZigzagVarint64 = p.prototype.ea),
    (p.prototype.$ = function () {
      return u(this.b == 1), this.a.$();
    }),
    (p.prototype.readFixedHash64 = p.prototype.$),
    (p.prototype.ta = function (t) {
      return u(this.b == 1), this.a.ta(t);
    }),
    (p.prototype.readSplitFixed64 = p.prototype.ta);
  function j(t, o) {
    u(t.b == 2);
    var e = t.a.o();
    e = t.a.B() + e;
    for (var r = []; t.a.B() < e; ) r.push(o.call(t.a));
    return r;
  }
  (p.prototype.gc = function () {
    return j(this, this.a.da);
  }),
    (p.prototype.readPackedInt32 = p.prototype.gc),
    (p.prototype.hc = function () {
      return j(this, this.a.Ea);
    }),
    (p.prototype.readPackedInt32String = p.prototype.hc),
    (p.prototype.ic = function () {
      return j(this, this.a.sa);
    }),
    (p.prototype.readPackedInt64 = p.prototype.ic),
    (p.prototype.jc = function () {
      return j(this, this.a.Fa);
    }),
    (p.prototype.readPackedInt64String = p.prototype.jc),
    (p.prototype.qc = function () {
      return j(this, this.a.o);
    }),
    (p.prototype.readPackedUint32 = p.prototype.qc),
    (p.prototype.rc = function () {
      return j(this, this.a.O);
    }),
    (p.prototype.readPackedUint32String = p.prototype.rc),
    (p.prototype.sc = function () {
      return j(this, this.a.Ga);
    }),
    (p.prototype.readPackedUint64 = p.prototype.sc),
    (p.prototype.tc = function () {
      return j(this, this.a.Ha);
    }),
    (p.prototype.readPackedUint64String = p.prototype.tc),
    (p.prototype.nc = function () {
      return j(this, this.a.Ia);
    }),
    (p.prototype.readPackedSint32 = p.prototype.nc),
    (p.prototype.oc = function () {
      return j(this, this.a.Ja);
    }),
    (p.prototype.readPackedSint64 = p.prototype.oc),
    (p.prototype.pc = function () {
      return j(this, this.a.Ka);
    }),
    (p.prototype.readPackedSint64String = p.prototype.pc),
    (p.prototype.bc = function () {
      return j(this, this.a.m);
    }),
    (p.prototype.readPackedFixed32 = p.prototype.bc),
    (p.prototype.cc = function () {
      return j(this, this.a.ga);
    }),
    (p.prototype.readPackedFixed64 = p.prototype.cc),
    (p.prototype.dc = function () {
      return j(this, this.a.ha);
    }),
    (p.prototype.readPackedFixed64String = p.prototype.dc),
    (p.prototype.kc = function () {
      return j(this, this.a.P);
    }),
    (p.prototype.readPackedSfixed32 = p.prototype.kc),
    (p.prototype.lc = function () {
      return j(this, this.a.ba);
    }),
    (p.prototype.readPackedSfixed64 = p.prototype.lc),
    (p.prototype.mc = function () {
      return j(this, this.a.ca);
    }),
    (p.prototype.readPackedSfixed64String = p.prototype.mc),
    (p.prototype.fc = function () {
      return j(this, this.a.aa);
    }),
    (p.prototype.readPackedFloat = p.prototype.fc),
    (p.prototype.$b = function () {
      return j(this, this.a.Z);
    }),
    (p.prototype.readPackedDouble = p.prototype.$b),
    (p.prototype.Zb = function () {
      return j(this, this.a.pa);
    }),
    (p.prototype.readPackedBool = p.prototype.Zb),
    (p.prototype.ac = function () {
      return j(this, this.a.ra);
    }),
    (p.prototype.readPackedEnum = p.prototype.ac),
    (p.prototype.uc = function () {
      return j(this, this.a.ia);
    }),
    (p.prototype.readPackedVarintHash64 = p.prototype.uc),
    (p.prototype.ec = function () {
      return j(this, this.a.$);
    }),
    (p.prototype.readPackedFixedHash64 = p.prototype.ec);
  function ot(t, o, e, r, n) {
    (this.ma = t), (this.Ba = o), (this.la = e), (this.Na = r), (this.na = n);
  }
  v("jspb.ExtensionFieldInfo", ot, void 0);
  function Jt(t, o, e, r, n, s) {
    (this.Za = t),
      (this.za = o),
      (this.Aa = e),
      (this.Wa = r),
      (this.Ab = n),
      (this.Nb = s);
  }
  v("jspb.ExtensionFieldBinaryInfo", Jt, void 0),
    (ot.prototype.F = function () {
      return !!this.la;
    }),
    (ot.prototype.isMessageType = ot.prototype.F);
  function d() {}
  v("jspb.Message", d, void 0),
    (d.GENERATE_TO_OBJECT = !0),
    (d.GENERATE_FROM_OBJECT = !0);
  var lt = typeof Uint8Array == "function";
  (d.prototype.Ib = function () {
    return this.b;
  }),
    (d.prototype.getJsPbMessageId = d.prototype.Ib),
    (d.initialize = function (t, o, e, r, n, s) {
      (t.f = null),
        o || (o = e ? [e] : []),
        (t.b = e ? String(e) : void 0),
        (t.D = e === 0 ? -1 : 0),
        (t.u = o);
      t: {
        if (
          ((e = t.u.length),
          (o = -1),
          e &&
            ((o = e - 1),
            (e = t.u[o]),
            !(
              e === null ||
              typeof e != "object" ||
              Array.isArray(e) ||
              (lt && e instanceof Uint8Array)
            )))
        ) {
          (t.G = o - t.D), (t.i = e);
          break t;
        }
        -1 < r
          ? ((t.G = Math.max(r, o + 1 - t.D)), (t.i = null))
          : (t.G = Number.MAX_VALUE);
      }
      if (((t.a = {}), n))
        for (r = 0; r < n.length; r++)
          (o = n[r]),
            o < t.G
              ? ((o += t.D), (t.u[o] = t.u[o] || et))
              : (ft(t), (t.i[o] = t.i[o] || et));
      if (s && s.length) for (r = 0; r < s.length; r++) St(t, s[r]);
    });
  var et = Object.freeze ? Object.freeze([]) : [];
  function ft(t) {
    var o = t.G + t.D;
    t.u[o] || (t.i = t.u[o] = {});
  }
  function Kt(t, o, e) {
    for (var r = [], n = 0; n < t.length; n++) r[n] = o.call(t[n], e, t[n]);
    return r;
  }
  (d.toObjectList = Kt),
    (d.toObjectExtension = function (t, o, e, r, n) {
      for (var s in e) {
        var l = e[s],
          w = r.call(t, l);
        if (w != null) {
          for (var x in l.Ba) if (l.Ba.hasOwnProperty(x)) break;
          o[x] = l.Na ? (l.na ? Kt(w, l.Na, n) : l.Na(n, w)) : w;
        }
      }
    }),
    (d.serializeBinaryExtensions = function (t, o, e, r) {
      for (var n in e) {
        var s = e[n],
          l = s.Za;
        if (!s.Aa)
          throw Error(
            "Message extension present that was generated without binary serialization support"
          );
        var w = r.call(t, l);
        if (w != null)
          if (l.F())
            if (s.Wa) s.Aa.call(o, l.ma, w, s.Wa);
            else
              throw Error(
                "Message extension present holding submessage without binary support enabled, and message is being serialized to binary format"
              );
          else s.Aa.call(o, l.ma, w);
      }
    }),
    (d.readBinaryExtension = function (t, o, e, r, n) {
      var s = e[o.c];
      if (s) {
        if (((e = s.Za), !s.za))
          throw Error(
            "Deserializing extension whose generated code does not support binary format"
          );
        if (e.F()) {
          var l = new e.la();
          s.za.call(o, l, s.Ab);
        } else l = s.za.call(o);
        e.na && !s.Nb
          ? (o = r.call(t, e))
            ? o.push(l)
            : n.call(t, e, [l])
          : n.call(t, e, l);
      } else o.C();
    });
  function C(t, o) {
    if (o < t.G) {
      o += t.D;
      var e = t.u[o];
      return e === et ? (t.u[o] = []) : e;
    }
    if (t.i) return (e = t.i[o]), e === et ? (t.i[o] = []) : e;
  }
  (d.getField = C),
    (d.getRepeatedField = function (t, o) {
      return C(t, o);
    });
  function Xt(t, o) {
    return (t = C(t, o)), t == null ? t : +t;
  }
  d.getOptionalFloatingPointField = Xt;
  function $t(t, o) {
    return (t = C(t, o)), t == null ? t : !!t;
  }
  (d.getBooleanField = $t),
    (d.getRepeatedFloatingPointField = function (t, o) {
      var e = C(t, o);
      if ((t.a || (t.a = {}), !t.a[o])) {
        for (var r = 0; r < e.length; r++) e[r] = +e[r];
        t.a[o] = !0;
      }
      return e;
    }),
    (d.getRepeatedBooleanField = function (t, o) {
      var e = C(t, o);
      if ((t.a || (t.a = {}), !t.a[o])) {
        for (var r = 0; r < e.length; r++) e[r] = !!e[r];
        t.a[o] = !0;
      }
      return e;
    });
  function Yt(t) {
    return t == null || typeof t == "string"
      ? t
      : lt && t instanceof Uint8Array
      ? Ut(t)
      : (B("Cannot coerce to b64 string: " + D(t)), null);
  }
  d.bytesAsB64 = Yt;
  function qt(t) {
    return t == null || t instanceof Uint8Array
      ? t
      : typeof t == "string"
      ? Dt(t)
      : (B("Cannot coerce to Uint8Array: " + D(t)), null);
  }
  (d.bytesAsU8 = qt),
    (d.bytesListAsB64 = function (t) {
      return Qt(t), t.length && typeof t[0] != "string" ? Z(t, Yt) : t;
    }),
    (d.bytesListAsU8 = function (t) {
      return Qt(t), !t.length || t[0] instanceof Uint8Array ? t : Z(t, qt);
    });
  function Qt(t) {
    if (t && 1 < t.length) {
      var o = D(t[0]);
      go(t, function (e) {
        D(e) != o &&
          B(
            "Inconsistent type in JSPB repeated field array. Got " +
              D(e) +
              " expected " +
              o
          );
      });
    }
  }
  function to(t, o, e) {
    return (t = C(t, o)), t == null ? e : t;
  }
  (d.getFieldWithDefault = to),
    (d.getBooleanFieldWithDefault = function (t, o, e) {
      return (t = $t(t, o)), t == null ? e : t;
    }),
    (d.getFloatingPointFieldWithDefault = function (t, o, e) {
      return (t = Xt(t, o)), t == null ? e : t;
    }),
    (d.getFieldProto3 = to),
    (d.getMapField = function (t, o, e, r) {
      if ((t.f || (t.f = {}), o in t.f)) return t.f[o];
      var n = C(t, o);
      if (!n) {
        if (e) return;
        (n = []), W(t, o, n);
      }
      return (t.f[o] = new P(n, r));
    });
  function W(t, o, e) {
    return O(t, d), o < t.G ? (t.u[o + t.D] = e) : (ft(t), (t.i[o] = e)), t;
  }
  (d.setField = W),
    (d.setProto3IntField = function (t, o, e) {
      return N(t, o, e, 0);
    }),
    (d.setProto3FloatField = function (t, o, e) {
      return N(t, o, e, 0);
    }),
    (d.setProto3BooleanField = function (t, o, e) {
      return N(t, o, e, !1);
    }),
    (d.setProto3StringField = function (t, o, e) {
      return N(t, o, e, "");
    }),
    (d.setProto3BytesField = function (t, o, e) {
      return N(t, o, e, "");
    }),
    (d.setProto3EnumField = function (t, o, e) {
      return N(t, o, e, 0);
    }),
    (d.setProto3StringIntField = function (t, o, e) {
      return N(t, o, e, "0");
    });
  function N(t, o, e, r) {
    return (
      O(t, d),
      e !== r
        ? W(t, o, e)
        : o < t.G
        ? (t.u[o + t.D] = null)
        : (ft(t), delete t.i[o]),
      t
    );
  }
  d.addToRepeatedField = function (t, o, e, r) {
    return O(t, d), (o = C(t, o)), r != null ? o.splice(r, 0, e) : o.push(e), t;
  };
  function oo(t, o, e, r) {
    return (
      O(t, d),
      (e = St(t, e)) &&
        e !== o &&
        r !== void 0 &&
        (t.f && e in t.f && (t.f[e] = void 0), W(t, e, void 0)),
      W(t, o, r)
    );
  }
  d.setOneofField = oo;
  function St(t, o) {
    for (var e, r, n = 0; n < o.length; n++) {
      var s = o[n],
        l = C(t, s);
      l != null && ((e = s), (r = l), W(t, s, void 0));
    }
    return e ? (W(t, e, r), e) : 0;
  }
  (d.computeOneofCase = St),
    (d.getWrapperField = function (t, o, e, r) {
      if ((t.f || (t.f = {}), !t.f[e])) {
        var n = C(t, e);
        (r || n) && (t.f[e] = new o(n));
      }
      return t.f[e];
    }),
    (d.getRepeatedWrapperField = function (t, o, e) {
      return eo(t, o, e), (o = t.f[e]), o == et && (o = t.f[e] = []), o;
    });
  function eo(t, o, e) {
    if ((t.f || (t.f = {}), !t.f[e])) {
      for (var r = C(t, e), n = [], s = 0; s < r.length; s++)
        n[s] = new o(r[s]);
      t.f[e] = n;
    }
  }
  (d.setWrapperField = function (t, o, e) {
    O(t, d), t.f || (t.f = {});
    var r = e && e.g();
    return (t.f[o] = e), W(t, o, r);
  }),
    (d.setOneofWrapperField = function (t, o, e, r) {
      O(t, d), t.f || (t.f = {});
      var n = r && r.g();
      return (t.f[o] = r), oo(t, o, e, n);
    }),
    (d.setRepeatedWrapperField = function (t, o, e) {
      O(t, d), t.f || (t.f = {}), (e = e || []);
      for (var r = [], n = 0; n < e.length; n++) r[n] = e[n].g();
      return (t.f[o] = e), W(t, o, r);
    }),
    (d.addToRepeatedWrapperField = function (t, o, e, r, n) {
      eo(t, r, o);
      var s = t.f[o];
      return (
        s || (s = t.f[o] = []),
        (e = e || new r()),
        (t = C(t, o)),
        n != null
          ? (s.splice(n, 0, e), t.splice(n, 0, e.g()))
          : (s.push(e), t.push(e.g())),
        e
      );
    }),
    (d.toMap = function (t, o, e, r) {
      for (var n = {}, s = 0; s < t.length; s++)
        n[o.call(t[s])] = e ? e.call(t[s], r, t[s]) : t[s];
      return n;
    });
  function ro(t) {
    if (t.f)
      for (var o in t.f) {
        var e = t.f[o];
        if (Array.isArray(e))
          for (var r = 0; r < e.length; r++) e[r] && e[r].g();
        else e && e.g();
      }
  }
  (d.prototype.g = function () {
    return ro(this), this.u;
  }),
    (d.prototype.toArray = d.prototype.g),
    (d.prototype.toString = function () {
      return ro(this), this.u.toString();
    }),
    (d.prototype.getExtension = function (t) {
      if (this.i) {
        this.f || (this.f = {});
        var o = t.ma;
        if (t.na) {
          if (t.F())
            return (
              this.f[o] ||
                (this.f[o] = Z(this.i[o] || [], function (e) {
                  return new t.la(e);
                })),
              this.f[o]
            );
        } else if (t.F())
          return (
            !this.f[o] && this.i[o] && (this.f[o] = new t.la(this.i[o])),
            this.f[o]
          );
        return this.i[o];
      }
    }),
    (d.prototype.getExtension = d.prototype.getExtension),
    (d.prototype.Kc = function (t, o) {
      this.f || (this.f = {}), ft(this);
      var e = t.ma;
      return (
        t.na
          ? ((o = o || []),
            t.F()
              ? ((this.f[e] = o),
                (this.i[e] = Z(o, function (r) {
                  return r.g();
                })))
              : (this.i[e] = o))
          : t.F()
          ? ((this.f[e] = o), (this.i[e] = o && o.g()))
          : (this.i[e] = o),
        this
      );
    }),
    (d.prototype.setExtension = d.prototype.Kc),
    (d.difference = function (t, o) {
      if (!(t instanceof o.constructor))
        throw Error("Messages have different types.");
      var e = t.g();
      o = o.g();
      var r = [],
        n = 0,
        s = e.length > o.length ? e.length : o.length;
      for (t.b && ((r[0] = t.b), (n = 1)); n < s; n++)
        rt(e[n], o[n]) || (r[n] = o[n]);
      return new t.constructor(r);
    }),
    (d.equals = function (t, o) {
      return (
        t == o ||
        (!(!t || !o) && t instanceof o.constructor && rt(t.g(), o.g()))
      );
    });
  function wt(t, o) {
    (t = t || {}), (o = o || {});
    var e = {},
      r;
    for (r in t) e[r] = 0;
    for (r in o) e[r] = 0;
    for (r in e) if (!rt(t[r], o[r])) return !1;
    return !0;
  }
  d.compareExtensions = wt;
  function rt(t, o) {
    if (t == o) return !0;
    if (!Et(t) || !Et(o))
      return (typeof t == "number" && isNaN(t)) ||
        (typeof o == "number" && isNaN(o))
        ? String(t) == String(o)
        : !1;
    if (t.constructor != o.constructor) return !1;
    if (lt && t.constructor === Uint8Array) {
      if (t.length != o.length) return !1;
      for (var e = 0; e < t.length; e++) if (t[e] != o[e]) return !1;
      return !0;
    }
    if (t.constructor === Array) {
      var r = void 0,
        n = void 0,
        s = Math.max(t.length, o.length);
      for (e = 0; e < s; e++) {
        var l = t[e],
          w = o[e];
        if (
          (l &&
            l.constructor == Object &&
            (u(r === void 0), u(e === t.length - 1), (r = l), (l = void 0)),
          w &&
            w.constructor == Object &&
            (u(n === void 0), u(e === o.length - 1), (n = w), (w = void 0)),
          !rt(l, w))
        )
          return !1;
      }
      return r || n ? ((r = r || {}), (n = n || {}), wt(r, n)) : !0;
    }
    if (t.constructor === Object) return wt(t, o);
    throw Error("Invalid type in JSPB array");
  }
  (d.compareFields = rt),
    (d.prototype.Bb = function () {
      return yt(this);
    }),
    (d.prototype.cloneMessage = d.prototype.Bb),
    (d.prototype.clone = function () {
      return yt(this);
    }),
    (d.prototype.clone = d.prototype.clone),
    (d.clone = function (t) {
      return yt(t);
    });
  function yt(t) {
    return new t.constructor(Ft(t.g()));
  }
  d.copyInto = function (t, o) {
    O(t, d),
      O(o, d),
      u(
        t.constructor == o.constructor,
        "Copy source and target message should have the same type."
      ),
      (t = yt(t));
    for (var e = o.g(), r = t.g(), n = (e.length = 0); n < r.length; n++)
      e[n] = r[n];
    (o.f = t.f), (o.i = t.i);
  };
  function Ft(t) {
    if (Array.isArray(t)) {
      for (var o = Array(t.length), e = 0; e < t.length; e++) {
        var r = t[e];
        r != null && (o[e] = typeof r == "object" ? Ft(u(r)) : r);
      }
      return o;
    }
    if (lt && t instanceof Uint8Array) return new Uint8Array(t);
    o = {};
    for (e in t)
      (r = t[e]), r != null && (o[e] = typeof r == "object" ? Ft(u(r)) : r);
    return o;
  }
  d.registerMessageType = function (t, o) {
    o.we = t;
  };
  var U = {
    dump: function (t) {
      return (
        O(t, d, "jspb.Message instance expected"),
        u(
          t.getExtension,
          "Only unobfuscated and unoptimized compilation modes supported."
        ),
        U.X(t)
      );
    },
  };
  v("jspb.debug.dump", U.dump, void 0),
    (U.X = function (t) {
      var o = D(t);
      if (
        o == "number" ||
        o == "string" ||
        o == "boolean" ||
        o == "null" ||
        o == "undefined" ||
        (typeof Uint8Array != "undefined" && t instanceof Uint8Array)
      )
        return t;
      if (o == "array") return wo(t), Z(t, U.X);
      if (t instanceof P) {
        var e = {};
        t = t.entries();
        for (var r = t.next(); !r.done; r = t.next())
          e[r.value[0]] = U.X(r.value[1]);
        return e;
      }
      O(t, d, "Only messages expected: " + t), (o = t.constructor);
      var n = { $name: o.name || o.displayName };
      for (w in o.prototype) {
        var s = /^get([A-Z]\w*)/.exec(w);
        if (s && w != "getExtension" && w != "getJsPbMessageId") {
          var l = "has" + s[1];
          (!t[l] || t[l]()) && ((l = t[w]()), (n[U.$a(s[1])] = U.X(l)));
        }
      }
      if (t.extensionObject_)
        return (
          (n.$extensions =
            "Recursive dumping of extensions not supported in compiled code. Switch to uncompiled or dump extension object directly"),
          n
        );
      for (r in o.extensions)
        if (/^\d+$/.test(r)) {
          l = o.extensions[r];
          var w = t.getExtension(l);
          (s = void 0), (l = l.Ba);
          var x = [],
            V = 0;
          for (s in l) x[V++] = s;
          (s = x[0]),
            w != null && (e || (e = n.$extensions = {}), (e[U.$a(s)] = U.X(w)));
        }
      return n;
    }),
    (U.$a = function (t) {
      return t.replace(/^[A-Z]/, function (o) {
        return o.toLowerCase();
      });
    });
  function f() {
    this.a = [];
  }
  v("jspb.BinaryEncoder", f, void 0),
    (f.prototype.length = function () {
      return this.a.length;
    }),
    (f.prototype.length = f.prototype.length),
    (f.prototype.end = function () {
      var t = this.a;
      return (this.a = []), t;
    }),
    (f.prototype.end = f.prototype.end),
    (f.prototype.l = function (t, o) {
      for (
        u(t == Math.floor(t)),
          u(o == Math.floor(o)),
          u(0 <= t && 4294967296 > t),
          u(0 <= o && 4294967296 > o);
        0 < o || 127 < t;

      )
        this.a.push((t & 127) | 128),
          (t = ((t >>> 7) | (o << 25)) >>> 0),
          (o >>>= 7);
      this.a.push(t);
    }),
    (f.prototype.writeSplitVarint64 = f.prototype.l),
    (f.prototype.A = function (t, o) {
      u(t == Math.floor(t)),
        u(o == Math.floor(o)),
        u(0 <= t && 4294967296 > t),
        u(0 <= o && 4294967296 > o),
        this.s(t),
        this.s(o);
    }),
    (f.prototype.writeSplitFixed64 = f.prototype.A),
    (f.prototype.j = function (t) {
      for (u(t == Math.floor(t)), u(0 <= t && 4294967296 > t); 127 < t; )
        this.a.push((t & 127) | 128), (t >>>= 7);
      this.a.push(t);
    }),
    (f.prototype.writeUnsignedVarint32 = f.prototype.j),
    (f.prototype.M = function (t) {
      if (
        (u(t == Math.floor(t)), u(-2147483648 <= t && 2147483648 > t), 0 <= t)
      )
        this.j(t);
      else {
        for (var o = 0; 9 > o; o++) this.a.push((t & 127) | 128), (t >>= 7);
        this.a.push(1);
      }
    }),
    (f.prototype.writeSignedVarint32 = f.prototype.M),
    (f.prototype.va = function (t) {
      u(t == Math.floor(t)),
        u(0 <= t && 18446744073709552e3 > t),
        X(t),
        this.l(M, b);
    }),
    (f.prototype.writeUnsignedVarint64 = f.prototype.va),
    (f.prototype.ua = function (t) {
      u(t == Math.floor(t)),
        u(-9223372036854776e3 <= t && 9223372036854776e3 > t),
        X(t),
        this.l(M, b);
    }),
    (f.prototype.writeSignedVarint64 = f.prototype.ua),
    (f.prototype.wa = function (t) {
      u(t == Math.floor(t)),
        u(-2147483648 <= t && 2147483648 > t),
        this.j(((t << 1) ^ (t >> 31)) >>> 0);
    }),
    (f.prototype.writeZigzagVarint32 = f.prototype.wa),
    (f.prototype.xa = function (t) {
      u(t == Math.floor(t)),
        u(-9223372036854776e3 <= t && 9223372036854776e3 > t),
        Rt(t),
        this.l(M, b);
    }),
    (f.prototype.writeZigzagVarint64 = f.prototype.xa),
    (f.prototype.Ta = function (t) {
      this.W(Q(t));
    }),
    (f.prototype.writeZigzagVarint64String = f.prototype.Ta),
    (f.prototype.W = function (t) {
      var o = this;
      T(t),
        pt(M, b, function (e, r) {
          o.l(e >>> 0, r >>> 0);
        });
    }),
    (f.prototype.writeZigzagVarintHash64 = f.prototype.W),
    (f.prototype.be = function (t) {
      u(t == Math.floor(t)), u(0 <= t && 256 > t), this.a.push((t >>> 0) & 255);
    }),
    (f.prototype.writeUint8 = f.prototype.be),
    (f.prototype.ae = function (t) {
      u(t == Math.floor(t)),
        u(0 <= t && 65536 > t),
        this.a.push((t >>> 0) & 255),
        this.a.push((t >>> 8) & 255);
    }),
    (f.prototype.writeUint16 = f.prototype.ae),
    (f.prototype.s = function (t) {
      u(t == Math.floor(t)),
        u(0 <= t && 4294967296 > t),
        this.a.push((t >>> 0) & 255),
        this.a.push((t >>> 8) & 255),
        this.a.push((t >>> 16) & 255),
        this.a.push((t >>> 24) & 255);
    }),
    (f.prototype.writeUint32 = f.prototype.s),
    (f.prototype.V = function (t) {
      u(t == Math.floor(t)),
        u(0 <= t && 18446744073709552e3 > t),
        vt(t),
        this.s(M),
        this.s(b);
    }),
    (f.prototype.writeUint64 = f.prototype.V),
    (f.prototype.Qc = function (t) {
      u(t == Math.floor(t)),
        u(-128 <= t && 128 > t),
        this.a.push((t >>> 0) & 255);
    }),
    (f.prototype.writeInt8 = f.prototype.Qc),
    (f.prototype.Pc = function (t) {
      u(t == Math.floor(t)),
        u(-32768 <= t && 32768 > t),
        this.a.push((t >>> 0) & 255),
        this.a.push((t >>> 8) & 255);
    }),
    (f.prototype.writeInt16 = f.prototype.Pc),
    (f.prototype.S = function (t) {
      u(t == Math.floor(t)),
        u(-2147483648 <= t && 2147483648 > t),
        this.a.push((t >>> 0) & 255),
        this.a.push((t >>> 8) & 255),
        this.a.push((t >>> 16) & 255),
        this.a.push((t >>> 24) & 255);
    }),
    (f.prototype.writeInt32 = f.prototype.S),
    (f.prototype.T = function (t) {
      u(t == Math.floor(t)),
        u(-9223372036854776e3 <= t && 9223372036854776e3 > t),
        X(t),
        this.A(M, b);
    }),
    (f.prototype.writeInt64 = f.prototype.T),
    (f.prototype.ka = function (t) {
      u(t == Math.floor(t)),
        u(-9223372036854776e3 <= +t && 9223372036854776e3 > +t),
        T(Q(t)),
        this.A(M, b);
    }),
    (f.prototype.writeInt64String = f.prototype.ka),
    (f.prototype.L = function (t) {
      u(
        t === 1 / 0 ||
          t === -1 / 0 ||
          isNaN(t) ||
          (-34028234663852886e22 <= t && 34028234663852886e22 >= t)
      ),
        Tt(t),
        this.s(M);
    }),
    (f.prototype.writeFloat = f.prototype.L),
    (f.prototype.J = function (t) {
      u(
        t === 1 / 0 ||
          t === -1 / 0 ||
          isNaN(t) ||
          (-17976931348623157e292 <= t && 17976931348623157e292 >= t)
      ),
        Nt(t),
        this.s(M),
        this.s(b);
    }),
    (f.prototype.writeDouble = f.prototype.J),
    (f.prototype.I = function (t) {
      u(typeof t == "boolean" || typeof t == "number"), this.a.push(t ? 1 : 0);
    }),
    (f.prototype.writeBool = f.prototype.I),
    (f.prototype.R = function (t) {
      u(t == Math.floor(t)), u(-2147483648 <= t && 2147483648 > t), this.M(t);
    }),
    (f.prototype.writeEnum = f.prototype.R),
    (f.prototype.ja = function (t) {
      this.a.push.apply(this.a, t);
    }),
    (f.prototype.writeBytes = f.prototype.ja),
    (f.prototype.N = function (t) {
      T(t), this.l(M, b);
    }),
    (f.prototype.writeVarintHash64 = f.prototype.N),
    (f.prototype.K = function (t) {
      T(t), this.s(M), this.s(b);
    }),
    (f.prototype.writeFixedHash64 = f.prototype.K),
    (f.prototype.U = function (t) {
      var o = this.a.length;
      So(t);
      for (var e = 0; e < t.length; e++) {
        var r = t.charCodeAt(e);
        if (128 > r) this.a.push(r);
        else if (2048 > r)
          this.a.push((r >> 6) | 192), this.a.push((r & 63) | 128);
        else if (65536 > r)
          if (55296 <= r && 56319 >= r && e + 1 < t.length) {
            var n = t.charCodeAt(e + 1);
            56320 <= n &&
              57343 >= n &&
              ((r = 1024 * (r - 55296) + n - 56320 + 65536),
              this.a.push((r >> 18) | 240),
              this.a.push(((r >> 12) & 63) | 128),
              this.a.push(((r >> 6) & 63) | 128),
              this.a.push((r & 63) | 128),
              e++);
          } else
            this.a.push((r >> 12) | 224),
              this.a.push(((r >> 6) & 63) | 128),
              this.a.push((r & 63) | 128);
      }
      return this.a.length - o;
    }),
    (f.prototype.writeString = f.prototype.U);
  function S(t, o) {
    (this.lo = t), (this.hi = o);
  }
  v("jspb.arith.UInt64", S, void 0),
    (S.prototype.cmp = function (t) {
      return this.hi < t.hi || (this.hi == t.hi && this.lo < t.lo)
        ? -1
        : this.hi == t.hi && this.lo == t.lo
        ? 0
        : 1;
    }),
    (S.prototype.cmp = S.prototype.cmp),
    (S.prototype.La = function () {
      return new S(
        ((this.lo >>> 1) | ((this.hi & 1) << 31)) >>> 0,
        (this.hi >>> 1) >>> 0
      );
    }),
    (S.prototype.rightShift = S.prototype.La),
    (S.prototype.Da = function () {
      return new S(
        (this.lo << 1) >>> 0,
        ((this.hi << 1) | (this.lo >>> 31)) >>> 0
      );
    }),
    (S.prototype.leftShift = S.prototype.Da),
    (S.prototype.cb = function () {
      return !!(this.hi & 2147483648);
    }),
    (S.prototype.msb = S.prototype.cb),
    (S.prototype.Ob = function () {
      return !!(this.lo & 1);
    }),
    (S.prototype.lsb = S.prototype.Ob),
    (S.prototype.Ua = function () {
      return this.lo == 0 && this.hi == 0;
    }),
    (S.prototype.zero = S.prototype.Ua),
    (S.prototype.add = function (t) {
      return new S(
        (((this.lo + t.lo) & 4294967295) >>> 0) >>> 0,
        ((((this.hi + t.hi) & 4294967295) >>> 0) +
          (4294967296 <= this.lo + t.lo ? 1 : 0)) >>>
          0
      );
    }),
    (S.prototype.add = S.prototype.add),
    (S.prototype.sub = function (t) {
      return new S(
        (((this.lo - t.lo) & 4294967295) >>> 0) >>> 0,
        ((((this.hi - t.hi) & 4294967295) >>> 0) -
          (0 > this.lo - t.lo ? 1 : 0)) >>>
          0
      );
    }),
    (S.prototype.sub = S.prototype.sub);
  function Pt(t, o) {
    var e = t & 65535;
    t >>>= 16;
    var r = o & 65535,
      n = o >>> 16;
    for (
      o = e * r + 65536 * ((e * n) & 65535) + 65536 * ((t * r) & 65535),
        e = t * n + ((e * n) >>> 16) + ((t * r) >>> 16);
      4294967296 <= o;

    )
      (o -= 4294967296), (e += 1);
    return new S(o >>> 0, e >>> 0);
  }
  (S.mul32x32 = Pt),
    (S.prototype.eb = function (t) {
      var o = Pt(this.lo, t);
      return (t = Pt(this.hi, t)), (t.hi = t.lo), (t.lo = 0), o.add(t);
    }),
    (S.prototype.mul = S.prototype.eb),
    (S.prototype.Xa = function (t) {
      if (t == 0) return [];
      var o = new S(0, 0),
        e = new S(this.lo, this.hi);
      t = new S(t, 0);
      for (var r = new S(1, 0); !t.cb(); ) (t = t.Da()), (r = r.Da());
      for (; !r.Ua(); )
        0 >= t.cmp(e) && ((o = o.add(r)), (e = e.sub(t))),
          (t = t.La()),
          (r = r.La());
      return [o, e];
    }),
    (S.prototype.div = S.prototype.Xa),
    (S.prototype.toString = function () {
      for (var t = "", o = this; !o.Ua(); ) {
        o = o.Xa(10);
        var e = o[0];
        (t = o[1].lo + t), (o = e);
      }
      return t == "" && (t = "0"), t;
    }),
    (S.prototype.toString = S.prototype.toString);
  function G(t) {
    for (var o = new S(0, 0), e = new S(0, 0), r = 0; r < t.length; r++) {
      if ("0" > t[r] || "9" < t[r]) return null;
      (e.lo = parseInt(t[r], 10)), (o = o.eb(10).add(e));
    }
    return o;
  }
  (S.fromString = G),
    (S.prototype.clone = function () {
      return new S(this.lo, this.hi);
    }),
    (S.prototype.clone = S.prototype.clone);
  function I(t, o) {
    (this.lo = t), (this.hi = o);
  }
  v("jspb.arith.Int64", I, void 0),
    (I.prototype.add = function (t) {
      return new I(
        (((this.lo + t.lo) & 4294967295) >>> 0) >>> 0,
        ((((this.hi + t.hi) & 4294967295) >>> 0) +
          (4294967296 <= this.lo + t.lo ? 1 : 0)) >>>
          0
      );
    }),
    (I.prototype.add = I.prototype.add),
    (I.prototype.sub = function (t) {
      return new I(
        (((this.lo - t.lo) & 4294967295) >>> 0) >>> 0,
        ((((this.hi - t.hi) & 4294967295) >>> 0) -
          (0 > this.lo - t.lo ? 1 : 0)) >>>
          0
      );
    }),
    (I.prototype.sub = I.prototype.sub),
    (I.prototype.clone = function () {
      return new I(this.lo, this.hi);
    }),
    (I.prototype.clone = I.prototype.clone),
    (I.prototype.toString = function () {
      var t = (this.hi & 2147483648) != 0,
        o = new S(this.lo, this.hi);
      return t && (o = new S(0, 0).sub(o)), (t ? "-" : "") + o.toString();
    }),
    (I.prototype.toString = I.prototype.toString);
  function ct(t) {
    var o = 0 < t.length && t[0] == "-";
    return (
      o && (t = t.substring(1)),
      (t = G(t)),
      t === null ? null : (o && (t = new S(0, 0).sub(t)), new I(t.lo, t.hi))
    );
  }
  I.fromString = ct;
  function i() {
    (this.c = []), (this.b = 0), (this.a = new f()), (this.h = []);
  }
  v("jspb.BinaryWriter", i, void 0);
  function io(t, o) {
    var e = t.a.end();
    t.c.push(e), t.c.push(o), (t.b += e.length + o.length);
  }
  function k(t, o) {
    return (
      F(t, o, 2),
      (o = t.a.end()),
      t.c.push(o),
      (t.b += o.length),
      o.push(t.b),
      o
    );
  }
  function E(t, o) {
    var e = o.pop();
    for (e = t.b + t.a.length() - e, u(0 <= e); 127 < e; )
      o.push((e & 127) | 128), (e >>>= 7), t.b++;
    o.push(e), t.b++;
  }
  (i.prototype.pb = function (t, o, e) {
    io(this, t.subarray(o, e));
  }),
    (i.prototype.writeSerializedMessage = i.prototype.pb),
    (i.prototype.Pb = function (t, o, e) {
      t != null && o != null && e != null && this.pb(t, o, e);
    }),
    (i.prototype.maybeWriteSerializedMessage = i.prototype.Pb),
    (i.prototype.reset = function () {
      (this.c = []), this.a.end(), (this.b = 0), (this.h = []);
    }),
    (i.prototype.reset = i.prototype.reset),
    (i.prototype.ab = function () {
      u(this.h.length == 0);
      for (
        var t = new Uint8Array(this.b + this.a.length()),
          o = this.c,
          e = o.length,
          r = 0,
          n = 0;
        n < e;
        n++
      ) {
        var s = o[n];
        t.set(s, r), (r += s.length);
      }
      return (
        (o = this.a.end()),
        t.set(o, r),
        (r += o.length),
        u(r == t.length),
        (this.c = [t]),
        t
      );
    }),
    (i.prototype.getResultBuffer = i.prototype.ab),
    (i.prototype.Kb = function (t) {
      return Ut(this.ab(), t);
    }),
    (i.prototype.getResultBase64String = i.prototype.Kb),
    (i.prototype.Va = function (t) {
      this.h.push(k(this, t));
    }),
    (i.prototype.beginSubMessage = i.prototype.Va),
    (i.prototype.Ya = function () {
      u(0 <= this.h.length), E(this, this.h.pop());
    }),
    (i.prototype.endSubMessage = i.prototype.Ya);
  function F(t, o, e) {
    u(1 <= o && o == Math.floor(o)), t.a.j(8 * o + e);
  }
  (i.prototype.Nc = function (t, o, e) {
    switch (t) {
      case 1:
        this.J(o, e);
        break;
      case 2:
        this.L(o, e);
        break;
      case 3:
        this.T(o, e);
        break;
      case 4:
        this.V(o, e);
        break;
      case 5:
        this.S(o, e);
        break;
      case 6:
        this.Qa(o, e);
        break;
      case 7:
        this.Pa(o, e);
        break;
      case 8:
        this.I(o, e);
        break;
      case 9:
        this.U(o, e);
        break;
      case 10:
        B("Group field type not supported in writeAny()");
        break;
      case 11:
        B("Message field type not supported in writeAny()");
        break;
      case 12:
        this.ja(o, e);
        break;
      case 13:
        this.s(o, e);
        break;
      case 14:
        this.R(o, e);
        break;
      case 15:
        this.Ra(o, e);
        break;
      case 16:
        this.Sa(o, e);
        break;
      case 17:
        this.rb(o, e);
        break;
      case 18:
        this.sb(o, e);
        break;
      case 30:
        this.K(o, e);
        break;
      case 31:
        this.N(o, e);
        break;
      default:
        B("Invalid field type in writeAny()");
    }
  }),
    (i.prototype.writeAny = i.prototype.Nc);
  function At(t, o, e) {
    e != null && (F(t, o, 0), t.a.j(e));
  }
  function Mt(t, o, e) {
    e != null && (F(t, o, 0), t.a.M(e));
  }
  (i.prototype.S = function (t, o) {
    o != null && (u(-2147483648 <= o && 2147483648 > o), Mt(this, t, o));
  }),
    (i.prototype.writeInt32 = i.prototype.S),
    (i.prototype.ob = function (t, o) {
      o != null &&
        ((o = parseInt(o, 10)),
        u(-2147483648 <= o && 2147483648 > o),
        Mt(this, t, o));
    }),
    (i.prototype.writeInt32String = i.prototype.ob),
    (i.prototype.T = function (t, o) {
      o != null &&
        (u(-9223372036854776e3 <= o && 9223372036854776e3 > o),
        o != null && (F(this, t, 0), this.a.ua(o)));
    }),
    (i.prototype.writeInt64 = i.prototype.T),
    (i.prototype.ka = function (t, o) {
      o != null && ((o = ct(o)), F(this, t, 0), this.a.l(o.lo, o.hi));
    }),
    (i.prototype.writeInt64String = i.prototype.ka),
    (i.prototype.s = function (t, o) {
      o != null && (u(0 <= o && 4294967296 > o), At(this, t, o));
    }),
    (i.prototype.writeUint32 = i.prototype.s),
    (i.prototype.ub = function (t, o) {
      o != null &&
        ((o = parseInt(o, 10)), u(0 <= o && 4294967296 > o), At(this, t, o));
    }),
    (i.prototype.writeUint32String = i.prototype.ub),
    (i.prototype.V = function (t, o) {
      o != null &&
        (u(0 <= o && 18446744073709552e3 > o),
        o != null && (F(this, t, 0), this.a.va(o)));
    }),
    (i.prototype.writeUint64 = i.prototype.V),
    (i.prototype.vb = function (t, o) {
      o != null && ((o = G(o)), F(this, t, 0), this.a.l(o.lo, o.hi));
    }),
    (i.prototype.writeUint64String = i.prototype.vb),
    (i.prototype.rb = function (t, o) {
      o != null &&
        (u(-2147483648 <= o && 2147483648 > o),
        o != null && (F(this, t, 0), this.a.wa(o)));
    }),
    (i.prototype.writeSint32 = i.prototype.rb),
    (i.prototype.sb = function (t, o) {
      o != null &&
        (u(-9223372036854776e3 <= o && 9223372036854776e3 > o),
        o != null && (F(this, t, 0), this.a.xa(o)));
    }),
    (i.prototype.writeSint64 = i.prototype.sb),
    (i.prototype.$d = function (t, o) {
      o != null && o != null && (F(this, t, 0), this.a.W(o));
    }),
    (i.prototype.writeSintHash64 = i.prototype.$d),
    (i.prototype.Zd = function (t, o) {
      o != null && o != null && (F(this, t, 0), this.a.Ta(o));
    }),
    (i.prototype.writeSint64String = i.prototype.Zd),
    (i.prototype.Pa = function (t, o) {
      o != null && (u(0 <= o && 4294967296 > o), F(this, t, 5), this.a.s(o));
    }),
    (i.prototype.writeFixed32 = i.prototype.Pa),
    (i.prototype.Qa = function (t, o) {
      o != null &&
        (u(0 <= o && 18446744073709552e3 > o), F(this, t, 1), this.a.V(o));
    }),
    (i.prototype.writeFixed64 = i.prototype.Qa),
    (i.prototype.nb = function (t, o) {
      o != null && ((o = G(o)), F(this, t, 1), this.a.A(o.lo, o.hi));
    }),
    (i.prototype.writeFixed64String = i.prototype.nb),
    (i.prototype.Ra = function (t, o) {
      o != null &&
        (u(-2147483648 <= o && 2147483648 > o), F(this, t, 5), this.a.S(o));
    }),
    (i.prototype.writeSfixed32 = i.prototype.Ra),
    (i.prototype.Sa = function (t, o) {
      o != null &&
        (u(-9223372036854776e3 <= o && 9223372036854776e3 > o),
        F(this, t, 1),
        this.a.T(o));
    }),
    (i.prototype.writeSfixed64 = i.prototype.Sa),
    (i.prototype.qb = function (t, o) {
      o != null && ((o = ct(o)), F(this, t, 1), this.a.A(o.lo, o.hi));
    }),
    (i.prototype.writeSfixed64String = i.prototype.qb),
    (i.prototype.L = function (t, o) {
      o != null && (F(this, t, 5), this.a.L(o));
    }),
    (i.prototype.writeFloat = i.prototype.L),
    (i.prototype.J = function (t, o) {
      o != null && (F(this, t, 1), this.a.J(o));
    }),
    (i.prototype.writeDouble = i.prototype.J),
    (i.prototype.I = function (t, o) {
      o != null &&
        (u(typeof o == "boolean" || typeof o == "number"),
        F(this, t, 0),
        this.a.I(o));
    }),
    (i.prototype.writeBool = i.prototype.I),
    (i.prototype.R = function (t, o) {
      o != null &&
        (u(-2147483648 <= o && 2147483648 > o), F(this, t, 0), this.a.M(o));
    }),
    (i.prototype.writeEnum = i.prototype.R),
    (i.prototype.U = function (t, o) {
      o != null && ((t = k(this, t)), this.a.U(o), E(this, t));
    }),
    (i.prototype.writeString = i.prototype.U),
    (i.prototype.ja = function (t, o) {
      o != null &&
        ((o = ut(o)), F(this, t, 2), this.a.j(o.length), io(this, o));
    }),
    (i.prototype.writeBytes = i.prototype.ja),
    (i.prototype.Rc = function (t, o, e) {
      o != null && ((t = k(this, t)), e(o, this), E(this, t));
    }),
    (i.prototype.writeMessage = i.prototype.Rc),
    (i.prototype.Sc = function (t, o, e) {
      o != null &&
        (F(this, 1, 3),
        F(this, 2, 0),
        this.a.M(t),
        (t = k(this, 3)),
        e(o, this),
        E(this, t),
        F(this, 1, 4));
    }),
    (i.prototype.writeMessageSet = i.prototype.Sc),
    (i.prototype.Oc = function (t, o, e) {
      o != null && (F(this, t, 3), e(o, this), F(this, t, 4));
    }),
    (i.prototype.writeGroup = i.prototype.Oc),
    (i.prototype.K = function (t, o) {
      o != null && (u(o.length == 8), F(this, t, 1), this.a.K(o));
    }),
    (i.prototype.writeFixedHash64 = i.prototype.K),
    (i.prototype.N = function (t, o) {
      o != null && (u(o.length == 8), F(this, t, 0), this.a.N(o));
    }),
    (i.prototype.writeVarintHash64 = i.prototype.N),
    (i.prototype.A = function (t, o, e) {
      F(this, t, 1), this.a.A(o, e);
    }),
    (i.prototype.writeSplitFixed64 = i.prototype.A),
    (i.prototype.l = function (t, o, e) {
      F(this, t, 0), this.a.l(o, e);
    }),
    (i.prototype.writeSplitVarint64 = i.prototype.l),
    (i.prototype.tb = function (t, o, e) {
      F(this, t, 0);
      var r = this.a;
      pt(o, e, function (n, s) {
        r.l(n >>> 0, s >>> 0);
      });
    }),
    (i.prototype.writeSplitZigzagVarint64 = i.prototype.tb),
    (i.prototype.Ed = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) Mt(this, t, o[e]);
    }),
    (i.prototype.writeRepeatedInt32 = i.prototype.Ed),
    (i.prototype.Fd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.ob(t, o[e]);
    }),
    (i.prototype.writeRepeatedInt32String = i.prototype.Fd),
    (i.prototype.Gd = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.ua(r));
        }
    }),
    (i.prototype.writeRepeatedInt64 = i.prototype.Gd),
    (i.prototype.Qd = function (t, o, e, r) {
      if (o != null)
        for (var n = 0; n < o.length; n++) this.A(t, e(o[n]), r(o[n]));
    }),
    (i.prototype.writeRepeatedSplitFixed64 = i.prototype.Qd),
    (i.prototype.Rd = function (t, o, e, r) {
      if (o != null)
        for (var n = 0; n < o.length; n++) this.l(t, e(o[n]), r(o[n]));
    }),
    (i.prototype.writeRepeatedSplitVarint64 = i.prototype.Rd),
    (i.prototype.Sd = function (t, o, e, r) {
      if (o != null)
        for (var n = 0; n < o.length; n++) this.tb(t, e(o[n]), r(o[n]));
    }),
    (i.prototype.writeRepeatedSplitZigzagVarint64 = i.prototype.Sd),
    (i.prototype.Hd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.ka(t, o[e]);
    }),
    (i.prototype.writeRepeatedInt64String = i.prototype.Hd),
    (i.prototype.Ud = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) At(this, t, o[e]);
    }),
    (i.prototype.writeRepeatedUint32 = i.prototype.Ud),
    (i.prototype.Vd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.ub(t, o[e]);
    }),
    (i.prototype.writeRepeatedUint32String = i.prototype.Vd),
    (i.prototype.Wd = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.va(r));
        }
    }),
    (i.prototype.writeRepeatedUint64 = i.prototype.Wd),
    (i.prototype.Xd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.vb(t, o[e]);
    }),
    (i.prototype.writeRepeatedUint64String = i.prototype.Xd),
    (i.prototype.Md = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.wa(r));
        }
    }),
    (i.prototype.writeRepeatedSint32 = i.prototype.Md),
    (i.prototype.Nd = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.xa(r));
        }
    }),
    (i.prototype.writeRepeatedSint64 = i.prototype.Nd),
    (i.prototype.Od = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.Ta(r));
        }
    }),
    (i.prototype.writeRepeatedSint64String = i.prototype.Od),
    (i.prototype.Pd = function (t, o) {
      if (o != null)
        for (var e = 0; e < o.length; e++) {
          var r = o[e];
          r != null && (F(this, t, 0), this.a.W(r));
        }
    }),
    (i.prototype.writeRepeatedSintHash64 = i.prototype.Pd),
    (i.prototype.yd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.Pa(t, o[e]);
    }),
    (i.prototype.writeRepeatedFixed32 = i.prototype.yd),
    (i.prototype.zd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.Qa(t, o[e]);
    }),
    (i.prototype.writeRepeatedFixed64 = i.prototype.zd),
    (i.prototype.Ad = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.nb(t, o[e]);
    }),
    (i.prototype.writeRepeatedFixed64String = i.prototype.Ad),
    (i.prototype.Jd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.Ra(t, o[e]);
    }),
    (i.prototype.writeRepeatedSfixed32 = i.prototype.Jd),
    (i.prototype.Kd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.Sa(t, o[e]);
    }),
    (i.prototype.writeRepeatedSfixed64 = i.prototype.Kd),
    (i.prototype.Ld = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.qb(t, o[e]);
    }),
    (i.prototype.writeRepeatedSfixed64String = i.prototype.Ld),
    (i.prototype.Cd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.L(t, o[e]);
    }),
    (i.prototype.writeRepeatedFloat = i.prototype.Cd),
    (i.prototype.wd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.J(t, o[e]);
    }),
    (i.prototype.writeRepeatedDouble = i.prototype.wd),
    (i.prototype.ud = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.I(t, o[e]);
    }),
    (i.prototype.writeRepeatedBool = i.prototype.ud),
    (i.prototype.xd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.R(t, o[e]);
    }),
    (i.prototype.writeRepeatedEnum = i.prototype.xd),
    (i.prototype.Td = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.U(t, o[e]);
    }),
    (i.prototype.writeRepeatedString = i.prototype.Td),
    (i.prototype.vd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.ja(t, o[e]);
    }),
    (i.prototype.writeRepeatedBytes = i.prototype.vd),
    (i.prototype.Id = function (t, o, e) {
      if (o != null)
        for (var r = 0; r < o.length; r++) {
          var n = k(this, t);
          e(o[r], this), E(this, n);
        }
    }),
    (i.prototype.writeRepeatedMessage = i.prototype.Id),
    (i.prototype.Dd = function (t, o, e) {
      if (o != null)
        for (var r = 0; r < o.length; r++)
          F(this, t, 3), e(o[r], this), F(this, t, 4);
    }),
    (i.prototype.writeRepeatedGroup = i.prototype.Dd),
    (i.prototype.Bd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.K(t, o[e]);
    }),
    (i.prototype.writeRepeatedFixedHash64 = i.prototype.Bd),
    (i.prototype.Yd = function (t, o) {
      if (o != null) for (var e = 0; e < o.length; e++) this.N(t, o[e]);
    }),
    (i.prototype.writeRepeatedVarintHash64 = i.prototype.Yd),
    (i.prototype.ad = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.M(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedInt32 = i.prototype.ad),
    (i.prototype.bd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.M(parseInt(o[e], 10));
        E(this, t);
      }
    }),
    (i.prototype.writePackedInt32String = i.prototype.bd),
    (i.prototype.cd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.ua(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedInt64 = i.prototype.cd),
    (i.prototype.md = function (t, o, e, r) {
      if (o != null) {
        t = k(this, t);
        for (var n = 0; n < o.length; n++) this.a.A(e(o[n]), r(o[n]));
        E(this, t);
      }
    }),
    (i.prototype.writePackedSplitFixed64 = i.prototype.md),
    (i.prototype.nd = function (t, o, e, r) {
      if (o != null) {
        t = k(this, t);
        for (var n = 0; n < o.length; n++) this.a.l(e(o[n]), r(o[n]));
        E(this, t);
      }
    }),
    (i.prototype.writePackedSplitVarint64 = i.prototype.nd),
    (i.prototype.od = function (t, o, e, r) {
      if (o != null) {
        t = k(this, t);
        for (var n = this.a, s = 0; s < o.length; s++)
          pt(e(o[s]), r(o[s]), function (l, w) {
            n.l(l >>> 0, w >>> 0);
          });
        E(this, t);
      }
    }),
    (i.prototype.writePackedSplitZigzagVarint64 = i.prototype.od),
    (i.prototype.dd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) {
          var r = ct(o[e]);
          this.a.l(r.lo, r.hi);
        }
        E(this, t);
      }
    }),
    (i.prototype.writePackedInt64String = i.prototype.dd),
    (i.prototype.pd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.j(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedUint32 = i.prototype.pd),
    (i.prototype.qd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.j(parseInt(o[e], 10));
        E(this, t);
      }
    }),
    (i.prototype.writePackedUint32String = i.prototype.qd),
    (i.prototype.rd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.va(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedUint64 = i.prototype.rd),
    (i.prototype.sd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) {
          var r = G(o[e]);
          this.a.l(r.lo, r.hi);
        }
        E(this, t);
      }
    }),
    (i.prototype.writePackedUint64String = i.prototype.sd),
    (i.prototype.hd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.wa(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedSint32 = i.prototype.hd),
    (i.prototype.jd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.xa(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedSint64 = i.prototype.jd),
    (i.prototype.kd = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.W(Q(o[e]));
        E(this, t);
      }
    }),
    (i.prototype.writePackedSint64String = i.prototype.kd),
    (i.prototype.ld = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.W(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedSintHash64 = i.prototype.ld),
    (i.prototype.Wc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(4 * o.length), t = 0; t < o.length; t++)
          this.a.s(o[t]);
    }),
    (i.prototype.writePackedFixed32 = i.prototype.Wc),
    (i.prototype.Xc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++)
          this.a.V(o[t]);
    }),
    (i.prototype.writePackedFixed64 = i.prototype.Xc),
    (i.prototype.Yc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++) {
          var e = G(o[t]);
          this.a.A(e.lo, e.hi);
        }
    }),
    (i.prototype.writePackedFixed64String = i.prototype.Yc),
    (i.prototype.ed = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(4 * o.length), t = 0; t < o.length; t++)
          this.a.S(o[t]);
    }),
    (i.prototype.writePackedSfixed32 = i.prototype.ed),
    (i.prototype.fd = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++)
          this.a.T(o[t]);
    }),
    (i.prototype.writePackedSfixed64 = i.prototype.fd),
    (i.prototype.gd = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++)
          this.a.ka(o[t]);
    }),
    (i.prototype.writePackedSfixed64String = i.prototype.gd),
    (i.prototype.$c = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(4 * o.length), t = 0; t < o.length; t++)
          this.a.L(o[t]);
    }),
    (i.prototype.writePackedFloat = i.prototype.$c),
    (i.prototype.Uc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++)
          this.a.J(o[t]);
    }),
    (i.prototype.writePackedDouble = i.prototype.Uc),
    (i.prototype.Tc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(o.length), t = 0; t < o.length; t++)
          this.a.I(o[t]);
    }),
    (i.prototype.writePackedBool = i.prototype.Tc),
    (i.prototype.Vc = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.R(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedEnum = i.prototype.Vc),
    (i.prototype.Zc = function (t, o) {
      if (o != null && o.length)
        for (F(this, t, 2), this.a.j(8 * o.length), t = 0; t < o.length; t++)
          this.a.K(o[t]);
    }),
    (i.prototype.writePackedFixedHash64 = i.prototype.Zc),
    (i.prototype.td = function (t, o) {
      if (o != null && o.length) {
        t = k(this, t);
        for (var e = 0; e < o.length; e++) this.a.N(o[e]);
        E(this, t);
      }
    }),
    (i.prototype.writePackedVarintHash64 = i.prototype.td),
    (h.debug = U),
    (h.Map = P),
    (h.Message = d),
    (h.BinaryReader = p),
    (h.BinaryWriter = i),
    (h.ExtensionFieldInfo = ot),
    (h.ExtensionFieldBinaryInfo = Jt),
    (h.exportSymbol = fo),
    (h.inherits = yo),
    (h.object = { extend: co }),
    (h.typeOf = D);
})(po);
const c = Po(po);
var _ = c,
  bt = function () {
    return this || window || bt || self || Function("return this")();
  }.call(null);
_.exportSymbol("proto.tutorial.ShowAreaPb", null, bt);
_.exportSymbol("proto.tutorial.ShowSeatPb", null, bt);
proto.tutorial.ShowSeatPb = function (h) {
  c.Message.initialize(this, h, 0, -1, null, null);
};
_.inherits(proto.tutorial.ShowSeatPb, c.Message);
_.DEBUG &&
  !COMPILED &&
  (proto.tutorial.ShowSeatPb.displayName = "proto.tutorial.ShowSeatPb");
proto.tutorial.ShowAreaPb = function (h) {
  c.Message.initialize(
    this,
    h,
    0,
    -1,
    proto.tutorial.ShowAreaPb.repeatedFields_,
    null
  );
};
_.inherits(proto.tutorial.ShowAreaPb, c.Message);
_.DEBUG &&
  !COMPILED &&
  (proto.tutorial.ShowAreaPb.displayName = "proto.tutorial.ShowAreaPb");
c.Message.GENERATE_TO_OBJECT &&
  ((proto.tutorial.ShowSeatPb.prototype.toObject = function (h) {
    return proto.tutorial.ShowSeatPb.toObject(h, this);
  }),
  (proto.tutorial.ShowSeatPb.toObject = function (h, y) {
    var g = {
      k: c.Message.getFieldWithDefault(y, 1, ""),
      a: c.Message.getFieldWithDefault(y, 2, 0),
      n: c.Message.getFieldWithDefault(y, 3, 0),
      g: c.Message.getFieldWithDefault(y, 4, ""),
      e: c.Message.getFieldWithDefault(y, 5, ""),
      b: c.Message.getFieldWithDefault(y, 6, ""),
      i: c.Message.getFieldWithDefault(y, 7, ""),
      s: c.Message.getFieldWithDefault(y, 8, ""),
      ai: c.Message.getFieldWithDefault(y, 9, ""),
      x: c.Message.getFieldWithDefault(y, 10, ""),
      y: c.Message.getFieldWithDefault(y, 11, ""),
    };
    return h && (g.$jspbMessageInstance = y), g;
  }));
proto.tutorial.ShowSeatPb.deserializeBinary = function (h) {
  var y = new c.BinaryReader(h),
    g = new proto.tutorial.ShowSeatPb();
  return proto.tutorial.ShowSeatPb.deserializeBinaryFromReader(g, y);
};
proto.tutorial.ShowSeatPb.deserializeBinaryFromReader = function (h, y) {
  for (; y.nextField() && !y.isEndGroup(); ) {
    var g = y.getFieldNumber();
    switch (g) {
      case 1:
        var A = y.readString();
        h.setK(A);
        break;
      case 2:
        var A = y.readUint64();
        h.setA(A);
        break;
      case 3:
        var A = y.readUint64();
        h.setN(A);
        break;
      case 4:
        var A = y.readString();
        h.setG(A);
        break;
      case 5:
        var A = y.readString();
        h.setE(A);
        break;
      case 6:
        var A = y.readString();
        h.setB(A);
        break;
      case 7:
        var A = y.readString();
        h.setI(A);
        break;
      case 8:
        var A = y.readString();
        h.setS(A);
        break;
      case 9:
        var A = y.readString();
        h.setAi(A);
        break;
      case 10:
        var A = y.readString();
        h.setX(A);
        break;
      case 11:
        var A = y.readString();
        h.setY(A);
        break;
      default:
        y.skipField();
        break;
    }
  }
  return h;
};
proto.tutorial.ShowSeatPb.prototype.serializeBinary = function () {
  var h = new c.BinaryWriter();
  return (
    proto.tutorial.ShowSeatPb.serializeBinaryToWriter(this, h),
    h.getResultBuffer()
  );
};
proto.tutorial.ShowSeatPb.serializeBinaryToWriter = function (h, y) {
  var g = void 0;
  (g = h.getK()),
    g.length > 0 && y.writeString(1, g),
    (g = h.getA()),
    g !== 0 && y.writeUint64(2, g),
    (g = h.getN()),
    g !== 0 && y.writeUint64(3, g),
    (g = h.getG()),
    g.length > 0 && y.writeString(4, g),
    (g = h.getE()),
    g.length > 0 && y.writeString(5, g),
    (g = h.getB()),
    g.length > 0 && y.writeString(6, g),
    (g = h.getI()),
    g.length > 0 && y.writeString(7, g),
    (g = h.getS()),
    g.length > 0 && y.writeString(8, g),
    (g = h.getAi()),
    g.length > 0 && y.writeString(9, g),
    (g = h.getX()),
    g.length > 0 && y.writeString(10, g),
    (g = h.getY()),
    g.length > 0 && y.writeString(11, g);
};
proto.tutorial.ShowSeatPb.prototype.getK = function () {
  return c.Message.getFieldWithDefault(this, 1, "");
};
proto.tutorial.ShowSeatPb.prototype.setK = function (h) {
  return c.Message.setProto3StringField(this, 1, h);
};
proto.tutorial.ShowSeatPb.prototype.getA = function () {
  return c.Message.getFieldWithDefault(this, 2, 0);
};
proto.tutorial.ShowSeatPb.prototype.setA = function (h) {
  return c.Message.setProto3IntField(this, 2, h);
};
proto.tutorial.ShowSeatPb.prototype.getN = function () {
  return c.Message.getFieldWithDefault(this, 3, 0);
};
proto.tutorial.ShowSeatPb.prototype.setN = function (h) {
  return c.Message.setProto3IntField(this, 3, h);
};
proto.tutorial.ShowSeatPb.prototype.getG = function () {
  return c.Message.getFieldWithDefault(this, 4, "");
};
proto.tutorial.ShowSeatPb.prototype.setG = function (h) {
  return c.Message.setProto3StringField(this, 4, h);
};
proto.tutorial.ShowSeatPb.prototype.getE = function () {
  return c.Message.getFieldWithDefault(this, 5, "");
};
proto.tutorial.ShowSeatPb.prototype.setE = function (h) {
  return c.Message.setProto3StringField(this, 5, h);
};
proto.tutorial.ShowSeatPb.prototype.getB = function () {
  return c.Message.getFieldWithDefault(this, 6, "");
};
proto.tutorial.ShowSeatPb.prototype.setB = function (h) {
  return c.Message.setProto3StringField(this, 6, h);
};
proto.tutorial.ShowSeatPb.prototype.getI = function () {
  return c.Message.getFieldWithDefault(this, 7, "");
};
proto.tutorial.ShowSeatPb.prototype.setI = function (h) {
  return c.Message.setProto3StringField(this, 7, h);
};
proto.tutorial.ShowSeatPb.prototype.getS = function () {
  return c.Message.getFieldWithDefault(this, 8, "");
};
proto.tutorial.ShowSeatPb.prototype.setS = function (h) {
  return c.Message.setProto3StringField(this, 8, h);
};
proto.tutorial.ShowSeatPb.prototype.getAi = function () {
  return c.Message.getFieldWithDefault(this, 9, "");
};
proto.tutorial.ShowSeatPb.prototype.setAi = function (h) {
  return c.Message.setProto3StringField(this, 9, h);
};
proto.tutorial.ShowSeatPb.prototype.getX = function () {
  return c.Message.getFieldWithDefault(this, 10, "");
};
proto.tutorial.ShowSeatPb.prototype.setX = function (h) {
  return c.Message.setProto3StringField(this, 10, h);
};
proto.tutorial.ShowSeatPb.prototype.getY = function () {
  return c.Message.getFieldWithDefault(this, 11, "");
};
proto.tutorial.ShowSeatPb.prototype.setY = function (h) {
  return c.Message.setProto3StringField(this, 11, h);
};
proto.tutorial.ShowAreaPb.repeatedFields_ = [9];
c.Message.GENERATE_TO_OBJECT &&
  ((proto.tutorial.ShowAreaPb.prototype.toObject = function (h) {
    return proto.tutorial.ShowAreaPb.toObject(h, this);
  }),
  (proto.tutorial.ShowAreaPb.toObject = function (h, y) {
    var g = {
      k: c.Message.getFieldWithDefault(y, 1, ""),
      a: c.Message.getFieldWithDefault(y, 2, ""),
      n: c.Message.getFieldWithDefault(y, 3, ""),
      g: c.Message.getFieldWithDefault(y, 4, ""),
      e: c.Message.getFieldWithDefault(y, 5, 0),
      b: c.Message.getFieldWithDefault(y, 6, 0),
      i: c.Message.getFieldWithDefault(y, 7, ""),
      s: c.Message.getFieldWithDefault(y, 8, ""),
      ai: c.Message.toObjectList(
        y.getAiList(),
        proto.tutorial.ShowSeatPb.toObject,
        h
      ),
    };
    return h && (g.$jspbMessageInstance = y), g;
  }));
proto.tutorial.ShowAreaPb.deserializeBinary = function (h) {
  var y = new c.BinaryReader(h),
    g = new proto.tutorial.ShowAreaPb();
  return proto.tutorial.ShowAreaPb.deserializeBinaryFromReader(g, y);
};
proto.tutorial.ShowAreaPb.deserializeBinaryFromReader = function (h, y) {
  for (; y.nextField() && !y.isEndGroup(); ) {
    var g = y.getFieldNumber();
    switch (g) {
      case 1:
        var A = y.readString();
        h.setK(A);
        break;
      case 2:
        var A = y.readString();
        h.setA(A);
        break;
      case 3:
        var A = y.readString();
        h.setN(A);
        break;
      case 4:
        var A = y.readString();
        h.setG(A);
        break;
      case 5:
        var A = y.readUint64();
        h.setE(A);
        break;
      case 6:
        var A = y.readUint64();
        h.setB(A);
        break;
      case 7:
        var A = y.readString();
        h.setI(A);
        break;
      case 8:
        var A = y.readString();
        h.setS(A);
        break;
      case 9:
        var A = new proto.tutorial.ShowSeatPb();
        y.readMessage(A, proto.tutorial.ShowSeatPb.deserializeBinaryFromReader),
          h.addAi(A);
        break;
      default:
        y.skipField();
        break;
    }
  }
  return h;
};
proto.tutorial.ShowAreaPb.prototype.serializeBinary = function () {
  var h = new c.BinaryWriter();
  return (
    proto.tutorial.ShowAreaPb.serializeBinaryToWriter(this, h),
    h.getResultBuffer()
  );
};
proto.tutorial.ShowAreaPb.serializeBinaryToWriter = function (h, y) {
  var g = void 0;
  (g = h.getK()),
    g.length > 0 && y.writeString(1, g),
    (g = h.getA()),
    g.length > 0 && y.writeString(2, g),
    (g = c.Message.getField(h, 3)),
    g != null && y.writeString(3, g),
    (g = c.Message.getField(h, 4)),
    g != null && y.writeString(4, g),
    (g = h.getE()),
    g !== 0 && y.writeUint64(5, g),
    (g = h.getB()),
    g !== 0 && y.writeUint64(6, g),
    (g = c.Message.getField(h, 7)),
    g != null && y.writeString(7, g),
    (g = c.Message.getField(h, 8)),
    g != null && y.writeString(8, g),
    (g = h.getAiList()),
    g.length > 0 &&
      y.writeRepeatedMessage(
        9,
        g,
        proto.tutorial.ShowSeatPb.serializeBinaryToWriter
      );
};
proto.tutorial.ShowAreaPb.prototype.getK = function () {
  return c.Message.getFieldWithDefault(this, 1, "");
};
proto.tutorial.ShowAreaPb.prototype.setK = function (h) {
  return c.Message.setProto3StringField(this, 1, h);
};
proto.tutorial.ShowAreaPb.prototype.getA = function () {
  return c.Message.getFieldWithDefault(this, 2, "");
};
proto.tutorial.ShowAreaPb.prototype.setA = function (h) {
  return c.Message.setProto3StringField(this, 2, h);
};
proto.tutorial.ShowAreaPb.prototype.getN = function () {
  return c.Message.getFieldWithDefault(this, 3, "");
};
proto.tutorial.ShowAreaPb.prototype.setN = function (h) {
  return c.Message.setField(this, 3, h);
};
proto.tutorial.ShowAreaPb.prototype.clearN = function () {
  return c.Message.setField(this, 3, void 0);
};
proto.tutorial.ShowAreaPb.prototype.hasN = function () {
  return c.Message.getField(this, 3) != null;
};
proto.tutorial.ShowAreaPb.prototype.getG = function () {
  return c.Message.getFieldWithDefault(this, 4, "");
};
proto.tutorial.ShowAreaPb.prototype.setG = function (h) {
  return c.Message.setField(this, 4, h);
};
proto.tutorial.ShowAreaPb.prototype.clearG = function () {
  return c.Message.setField(this, 4, void 0);
};
proto.tutorial.ShowAreaPb.prototype.hasG = function () {
  return c.Message.getField(this, 4) != null;
};
proto.tutorial.ShowAreaPb.prototype.getE = function () {
  return c.Message.getFieldWithDefault(this, 5, 0);
};
proto.tutorial.ShowAreaPb.prototype.setE = function (h) {
  return c.Message.setProto3IntField(this, 5, h);
};
proto.tutorial.ShowAreaPb.prototype.getB = function () {
  return c.Message.getFieldWithDefault(this, 6, 0);
};
proto.tutorial.ShowAreaPb.prototype.setB = function (h) {
  return c.Message.setProto3IntField(this, 6, h);
};
proto.tutorial.ShowAreaPb.prototype.getI = function () {
  return c.Message.getFieldWithDefault(this, 7, "");
};
proto.tutorial.ShowAreaPb.prototype.setI = function (h) {
  return c.Message.setField(this, 7, h);
};
proto.tutorial.ShowAreaPb.prototype.clearI = function () {
  return c.Message.setField(this, 7, void 0);
};
proto.tutorial.ShowAreaPb.prototype.hasI = function () {
  return c.Message.getField(this, 7) != null;
};
proto.tutorial.ShowAreaPb.prototype.getS = function () {
  return c.Message.getFieldWithDefault(this, 8, "");
};
proto.tutorial.ShowAreaPb.prototype.setS = function (h) {
  return c.Message.setField(this, 8, h);
};
proto.tutorial.ShowAreaPb.prototype.clearS = function () {
  return c.Message.setField(this, 8, void 0);
};
proto.tutorial.ShowAreaPb.prototype.hasS = function () {
  return c.Message.getField(this, 8) != null;
};
proto.tutorial.ShowAreaPb.prototype.getAiList = function () {
  return c.Message.getRepeatedWrapperField(this, proto.tutorial.ShowSeatPb, 9);
};
proto.tutorial.ShowAreaPb.prototype.setAiList = function (h) {
  return c.Message.setRepeatedWrapperField(this, 9, h);
};
proto.tutorial.ShowAreaPb.prototype.addAi = function (h, y) {
  return c.Message.addToRepeatedWrapperField(
    this,
    9,
    h,
    proto.tutorial.ShowSeatPb,
    y
  );
};
proto.tutorial.ShowAreaPb.prototype.clearAiList = function () {
  return this.setAiList([]);
};
const Mo = proto.tutorial;
export { Mo as default };
