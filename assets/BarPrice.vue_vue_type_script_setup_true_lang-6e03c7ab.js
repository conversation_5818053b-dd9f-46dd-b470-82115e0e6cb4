import {
  V as st,
  d as G,
  w as Yt,
  e as an,
  ab as on,
  g as m,
  a1 as Xt,
  a2 as ln,
  a3 as cn,
  $ as rt,
  ac as F,
  ad as un,
  ae as de,
  I as hn,
  L as fn,
  af as vn,
  W as Ee,
  ag as dn,
  ah as pn,
  ai as Tn,
  r as mn,
  aj as gn,
  ak as En,
  al as ye,
  a5 as Pt,
  am as yn,
  an as _n,
  Y as pe,
  F as _e,
  G as In,
  o as Ft,
  c as Ht,
  i as Wt,
  B as Pn,
  a7 as Cn,
  R as On,
  y as Te,
} from "./index-f027b512.js";
let it = 0;
function Sn(a) {
  a
    ? (it || document.body.classList.add("van-toast--unclickable"), it++)
    : it &&
      (it--, it || document.body.classList.remove("van-toast--unclickable"));
}
const [Nn, B] = st("toast"),
  An = [
    "show",
    "overlay",
    "teleport",
    "transition",
    "overlayClass",
    "overlayStyle",
    "closeOnClickOverlay",
    "zIndex",
  ],
  Dn = {
    icon: String,
    show: Boolean,
    type: rt("text"),
    overlay: Boolean,
    message: F,
    iconSize: F,
    duration: un(2e3),
    position: rt("middle"),
    teleport: [String, Object],
    wordBreak: String,
    className: de,
    iconPrefix: String,
    transition: rt("van-fade"),
    loadingType: String,
    forbidClick: Boolean,
    overlayClass: de,
    overlayStyle: Object,
    closeOnClick: Boolean,
    closeOnClickOverlay: Boolean,
    zIndex: F,
  };
var bn = G({
  name: Nn,
  props: Dn,
  emits: ["update:show"],
  setup(a, { emit: l, slots: y }) {
    let f,
      c = !1;
    const D = () => {
        const I = a.show && a.forbidClick;
        c !== I && ((c = I), Sn(c));
      },
      U = (I) => l("update:show", I),
      H = () => {
        a.closeOnClick && U(!1);
      },
      v = () => clearTimeout(f),
      u = () => {
        const {
          icon: I,
          type: T,
          iconSize: C,
          iconPrefix: at,
          loadingType: O,
        } = a;
        if (I || T === "success" || T === "fail")
          return m(
            hn,
            { name: I || T, size: C, class: B("icon"), classPrefix: at },
            null
          );
        if (T === "loading")
          return m(fn, { class: B("loading"), size: C, type: O }, null);
      },
      $ = () => {
        const { type: I, message: T } = a;
        if (y.message) return m("div", { class: B("text") }, [y.message()]);
        if (vn(T) && T !== "")
          return I === "html"
            ? m("div", { key: 0, class: B("text"), innerHTML: String(T) }, null)
            : m("div", { class: B("text") }, [T]);
      };
    return (
      Yt(() => [a.show, a.forbidClick], D),
      Yt(
        () => [a.show, a.type, a.message, a.duration],
        () => {
          v(),
            a.show &&
              a.duration > 0 &&
              (f = setTimeout(() => {
                U(!1);
              }, a.duration));
        }
      ),
      an(D),
      on(D),
      () =>
        m(
          cn,
          Xt(
            {
              class: [
                B([
                  a.position,
                  a.wordBreak === "normal" ? "break-normal" : a.wordBreak,
                  { [a.type]: !a.icon },
                ]),
                a.className,
              ],
              lockScroll: !1,
              onClick: H,
              onClosed: v,
              "onUpdate:show": U,
            },
            ln(a, An)
          ),
          { default: () => [u(), $()] }
        )
    );
  },
});
const Mn = {
  icon: "",
  type: "text",
  message: "",
  className: "",
  overlay: !1,
  onClose: void 0,
  onOpened: void 0,
  duration: 2e3,
  teleport: "body",
  iconSize: void 0,
  iconPrefix: void 0,
  position: "middle",
  transition: "van-fade",
  forbidClick: !1,
  loadingType: void 0,
  overlayClass: "",
  overlayStyle: void 0,
  closeOnClick: !1,
  closeOnClickOverlay: !1,
};
let It = [],
  kn = !1,
  me = Ee({}, Mn);
const Un = new Map();
function xn(a) {
  return pn(a) ? a : { message: a };
}
function wn() {
  const { instance: a, unmount: l } = Tn({
    setup() {
      const y = mn(""),
        { open: f, state: c, close: D, toggle: U } = gn(),
        H = () => {},
        v = () => m(bn, Xt(c, { onClosed: H, "onUpdate:show": U }), null);
      return (
        Yt(y, (u) => {
          c.message = u;
        }),
        (En().render = v),
        { open: f, close: D, message: y }
      );
    },
  });
  return a;
}
function Rn() {
  if (!It.length || kn) {
    const a = wn();
    It.push(a);
  }
  return It[It.length - 1];
}
function ui(a = {}) {
  if (!dn) return {};
  const l = Rn(),
    y = xn(a);
  return l.open(Ee({}, me, Un.get(y.type || me.type), y)), l;
}
const [Ln, Fn] = st("skeleton-title"),
  Hn = { round: Boolean, titleWidth: F };
var Wn = G({
  name: Ln,
  props: Hn,
  setup(a) {
    return () =>
      m(
        "h3",
        { class: Fn([{ round: a.round }]), style: { width: ye(a.titleWidth) } },
        null
      );
  },
});
const Yn = Pt(Wn);
var Xn = Yn;
const [Vn, zn] = st("skeleton-avatar"),
  qn = { avatarSize: F, avatarShape: rt("round") };
var Bn = G({
  name: Vn,
  props: qn,
  setup(a) {
    return () =>
      m("div", { class: zn([a.avatarShape]), style: yn(a.avatarSize) }, null);
  },
});
const Gn = Pt(Bn);
var $n = Gn;
const Vt = "100%",
  Zn = { round: Boolean, rowWidth: { type: F, default: Vt } },
  [jn, Jn] = st("skeleton-paragraph");
var Qn = G({
  name: jn,
  props: Zn,
  setup(a) {
    return () =>
      m(
        "div",
        { class: Jn([{ round: a.round }]), style: { width: a.rowWidth } },
        null
      );
  },
});
const Kn = Pt(Qn);
var ti = Kn;
const [ei, ge] = st("skeleton"),
  ni = "60%",
  ii = {
    row: _n(0),
    round: Boolean,
    title: Boolean,
    titleWidth: F,
    avatar: Boolean,
    avatarSize: F,
    avatarShape: rt("round"),
    loading: pe,
    animate: pe,
    rowWidth: { type: [Number, String, Array], default: Vt },
  };
var ri = G({
  name: ei,
  inheritAttrs: !1,
  props: ii,
  setup(a, { slots: l, attrs: y }) {
    const f = () => {
        if (a.avatar)
          return m(
            $n,
            { avatarShape: a.avatarShape, avatarSize: a.avatarSize },
            null
          );
      },
      c = () => {
        if (a.title)
          return m(Xn, { round: a.round, titleWidth: a.titleWidth }, null);
      },
      D = (v) => {
        const { rowWidth: u } = a;
        return u === Vt && v === +a.row - 1 ? ni : Array.isArray(u) ? u[v] : u;
      },
      U = () =>
        Array(+a.row)
          .fill("")
          .map((v, u) =>
            m(ti, { key: u, round: a.round, rowWidth: ye(D(u)) }, null)
          ),
      H = () =>
        l.template
          ? l.template()
          : m(_e, null, [f(), m("div", { class: ge("content") }, [c(), U()])]);
    return () => {
      var v;
      return a.loading
        ? m(
            "div",
            Xt({ class: ge({ animate: a.animate, round: a.round }) }, y),
            [H()]
          )
        : (v = l.default) == null
        ? void 0
        : v.call(l);
    };
  },
});
const hi = Pt(ri);
var Ie = { exports: {} };
/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 Jorik Tangelder;
 * Licensed under the MIT license */ (function (a) {
  (function (l, y, f, c) {
    var D = ["", "webkit", "Moz", "MS", "ms", "o"],
      U = y.createElement("div"),
      H = "function",
      v = Math.round,
      u = Math.abs,
      $ = Date.now;
    function I(t, e, n) {
      return setTimeout(Ot(t, n), e);
    }
    function T(t, e, n) {
      return Array.isArray(t) ? (C(t, n[e], n), !0) : !1;
    }
    function C(t, e, n) {
      var i;
      if (t)
        if (t.forEach) t.forEach(e, n);
        else if (t.length !== c)
          for (i = 0; i < t.length; ) e.call(n, t[i], i, t), i++;
        else for (i in t) t.hasOwnProperty(i) && e.call(n, t[i], i, t);
    }
    function at(t, e, n) {
      var i =
        "DEPRECATED METHOD: " +
        e +
        `
` +
        n +
        ` AT 
`;
      return function () {
        var r = new Error("get-stack-trace"),
          s =
            r && r.stack
              ? r.stack
                  .replace(/^[^\(]+?[\n$]/gm, "")
                  .replace(/^\s+at\s+/gm, "")
                  .replace(/^Object.<anonymous>\s*\(/gm, "{anonymous}()@")
              : "Unknown Stack Trace",
          o = l.console && (l.console.warn || l.console.log);
        return o && o.call(l.console, i, s), t.apply(this, arguments);
      };
    }
    var O;
    typeof Object.assign != "function"
      ? (O = function (e) {
          if (e === c || e === null)
            throw new TypeError("Cannot convert undefined or null to object");
          for (var n = Object(e), i = 1; i < arguments.length; i++) {
            var r = arguments[i];
            if (r !== c && r !== null)
              for (var s in r) r.hasOwnProperty(s) && (n[s] = r[s]);
          }
          return n;
        })
      : (O = Object.assign);
    var Ct = at(
        function (e, n, i) {
          for (var r = Object.keys(n), s = 0; s < r.length; )
            (!i || (i && e[r[s]] === c)) && (e[r[s]] = n[r[s]]), s++;
          return e;
        },
        "extend",
        "Use `assign`."
      ),
      Pe = at(
        function (e, n) {
          return Ct(e, n, !0);
        },
        "merge",
        "Use `assign`."
      );
    function P(t, e, n) {
      var i = e.prototype,
        r;
      (r = t.prototype = Object.create(i)),
        (r.constructor = t),
        (r._super = i),
        n && O(r, n);
    }
    function Ot(t, e) {
      return function () {
        return t.apply(e, arguments);
      };
    }
    function St(t, e) {
      return typeof t == H ? t.apply((e && e[0]) || c, e) : t;
    }
    function zt(t, e) {
      return t === c ? e : t;
    }
    function ot(t, e, n) {
      C(ct(e), function (i) {
        t.addEventListener(i, n, !1);
      });
    }
    function lt(t, e, n) {
      C(ct(e), function (i) {
        t.removeEventListener(i, n, !1);
      });
    }
    function qt(t, e) {
      for (; t; ) {
        if (t == e) return !0;
        t = t.parentNode;
      }
      return !1;
    }
    function W(t, e) {
      return t.indexOf(e) > -1;
    }
    function ct(t) {
      return t.trim().split(/\s+/g);
    }
    function z(t, e, n) {
      if (t.indexOf && !n) return t.indexOf(e);
      for (var i = 0; i < t.length; ) {
        if ((n && t[i][n] == e) || (!n && t[i] === e)) return i;
        i++;
      }
      return -1;
    }
    function ut(t) {
      return Array.prototype.slice.call(t, 0);
    }
    function Bt(t, e, n) {
      for (var i = [], r = [], s = 0; s < t.length; ) {
        var o = e ? t[s][e] : t[s];
        z(r, o) < 0 && i.push(t[s]), (r[s] = o), s++;
      }
      return (
        n &&
          (e
            ? (i = i.sort(function (p, E) {
                return p[e] > E[e];
              }))
            : (i = i.sort())),
        i
      );
    }
    function ht(t, e) {
      for (
        var n, i, r = e[0].toUpperCase() + e.slice(1), s = 0;
        s < D.length;

      ) {
        if (((n = D[s]), (i = n ? n + r : e), i in t)) return i;
        s++;
      }
      return c;
    }
    var Ce = 1;
    function Oe() {
      return Ce++;
    }
    function Gt(t) {
      var e = t.ownerDocument || t;
      return e.defaultView || e.parentWindow || l;
    }
    var Se = /mobile|tablet|ip(ad|hone|od)|android/i,
      $t = "ontouchstart" in l,
      Ne = ht(l, "PointerEvent") !== c,
      Ae = $t && Se.test(navigator.userAgent),
      Z = "touch",
      De = "pen",
      Nt = "mouse",
      be = "kinect",
      Me = 25,
      g = 1,
      Y = 2,
      h = 4,
      _ = 8,
      ft = 1,
      j = 2,
      J = 4,
      Q = 8,
      K = 16,
      b = j | J,
      X = Q | K,
      Zt = b | X,
      jt = ["x", "y"],
      vt = ["clientX", "clientY"];
    function S(t, e) {
      var n = this;
      (this.manager = t),
        (this.callback = e),
        (this.element = t.element),
        (this.target = t.options.inputTarget),
        (this.domHandler = function (i) {
          St(t.options.enable, [t]) && n.handler(i);
        }),
        this.init();
    }
    S.prototype = {
      handler: function () {},
      init: function () {
        this.evEl && ot(this.element, this.evEl, this.domHandler),
          this.evTarget && ot(this.target, this.evTarget, this.domHandler),
          this.evWin && ot(Gt(this.element), this.evWin, this.domHandler);
      },
      destroy: function () {
        this.evEl && lt(this.element, this.evEl, this.domHandler),
          this.evTarget && lt(this.target, this.evTarget, this.domHandler),
          this.evWin && lt(Gt(this.element), this.evWin, this.domHandler);
      },
    };
    function ke(t) {
      var e,
        n = t.options.inputClass;
      return (
        n ? (e = n) : Ne ? (e = Dt) : Ae ? (e = Tt) : $t ? (e = bt) : (e = pt),
        new e(t, Ue)
      );
    }
    function Ue(t, e, n) {
      var i = n.pointers.length,
        r = n.changedPointers.length,
        s = e & g && i - r === 0,
        o = e & (h | _) && i - r === 0;
      (n.isFirst = !!s),
        (n.isFinal = !!o),
        s && (t.session = {}),
        (n.eventType = e),
        xe(t, n),
        t.emit("hammer.input", n),
        t.recognize(n),
        (t.session.prevInput = n);
    }
    function xe(t, e) {
      var n = t.session,
        i = e.pointers,
        r = i.length;
      n.firstInput || (n.firstInput = Jt(e)),
        r > 1 && !n.firstMultiple
          ? (n.firstMultiple = Jt(e))
          : r === 1 && (n.firstMultiple = !1);
      var s = n.firstInput,
        o = n.firstMultiple,
        d = o ? o.center : s.center,
        p = (e.center = Qt(i));
      (e.timeStamp = $()),
        (e.deltaTime = e.timeStamp - s.timeStamp),
        (e.angle = At(d, p)),
        (e.distance = dt(d, p)),
        we(n, e),
        (e.offsetDirection = te(e.deltaX, e.deltaY));
      var E = Kt(e.deltaTime, e.deltaX, e.deltaY);
      (e.overallVelocityX = E.x),
        (e.overallVelocityY = E.y),
        (e.overallVelocity = u(E.x) > u(E.y) ? E.x : E.y),
        (e.scale = o ? Fe(o.pointers, i) : 1),
        (e.rotation = o ? Le(o.pointers, i) : 0),
        (e.maxPointers = n.prevInput
          ? e.pointers.length > n.prevInput.maxPointers
            ? e.pointers.length
            : n.prevInput.maxPointers
          : e.pointers.length),
        Re(n, e);
      var k = t.element;
      qt(e.srcEvent.target, k) && (k = e.srcEvent.target), (e.target = k);
    }
    function we(t, e) {
      var n = e.center,
        i = t.offsetDelta || {},
        r = t.prevDelta || {},
        s = t.prevInput || {};
      (e.eventType === g || s.eventType === h) &&
        ((r = t.prevDelta = { x: s.deltaX || 0, y: s.deltaY || 0 }),
        (i = t.offsetDelta = { x: n.x, y: n.y })),
        (e.deltaX = r.x + (n.x - i.x)),
        (e.deltaY = r.y + (n.y - i.y));
    }
    function Re(t, e) {
      var n = t.lastInterval || e,
        i = e.timeStamp - n.timeStamp,
        r,
        s,
        o,
        d;
      if (e.eventType != _ && (i > Me || n.velocity === c)) {
        var p = e.deltaX - n.deltaX,
          E = e.deltaY - n.deltaY,
          k = Kt(i, p, E);
        (s = k.x),
          (o = k.y),
          (r = u(k.x) > u(k.y) ? k.x : k.y),
          (d = te(p, E)),
          (t.lastInterval = e);
      } else
        (r = n.velocity),
          (s = n.velocityX),
          (o = n.velocityY),
          (d = n.direction);
      (e.velocity = r), (e.velocityX = s), (e.velocityY = o), (e.direction = d);
    }
    function Jt(t) {
      for (var e = [], n = 0; n < t.pointers.length; )
        (e[n] = {
          clientX: v(t.pointers[n].clientX),
          clientY: v(t.pointers[n].clientY),
        }),
          n++;
      return {
        timeStamp: $(),
        pointers: e,
        center: Qt(e),
        deltaX: t.deltaX,
        deltaY: t.deltaY,
      };
    }
    function Qt(t) {
      var e = t.length;
      if (e === 1) return { x: v(t[0].clientX), y: v(t[0].clientY) };
      for (var n = 0, i = 0, r = 0; r < e; )
        (n += t[r].clientX), (i += t[r].clientY), r++;
      return { x: v(n / e), y: v(i / e) };
    }
    function Kt(t, e, n) {
      return { x: e / t || 0, y: n / t || 0 };
    }
    function te(t, e) {
      return t === e ? ft : u(t) >= u(e) ? (t < 0 ? j : J) : e < 0 ? Q : K;
    }
    function dt(t, e, n) {
      n || (n = jt);
      var i = e[n[0]] - t[n[0]],
        r = e[n[1]] - t[n[1]];
      return Math.sqrt(i * i + r * r);
    }
    function At(t, e, n) {
      n || (n = jt);
      var i = e[n[0]] - t[n[0]],
        r = e[n[1]] - t[n[1]];
      return (Math.atan2(r, i) * 180) / Math.PI;
    }
    function Le(t, e) {
      return At(e[1], e[0], vt) + At(t[1], t[0], vt);
    }
    function Fe(t, e) {
      return dt(e[0], e[1], vt) / dt(t[0], t[1], vt);
    }
    var He = { mousedown: g, mousemove: Y, mouseup: h },
      We = "mousedown",
      Ye = "mousemove mouseup";
    function pt() {
      (this.evEl = We),
        (this.evWin = Ye),
        (this.pressed = !1),
        S.apply(this, arguments);
    }
    P(pt, S, {
      handler: function (e) {
        var n = He[e.type];
        n & g && e.button === 0 && (this.pressed = !0),
          n & Y && e.which !== 1 && (n = h),
          this.pressed &&
            (n & h && (this.pressed = !1),
            this.callback(this.manager, n, {
              pointers: [e],
              changedPointers: [e],
              pointerType: Nt,
              srcEvent: e,
            }));
      },
    });
    var Xe = {
        pointerdown: g,
        pointermove: Y,
        pointerup: h,
        pointercancel: _,
        pointerout: _,
      },
      Ve = { 2: Z, 3: De, 4: Nt, 5: be },
      ee = "pointerdown",
      ne = "pointermove pointerup pointercancel";
    l.MSPointerEvent &&
      !l.PointerEvent &&
      ((ee = "MSPointerDown"),
      (ne = "MSPointerMove MSPointerUp MSPointerCancel"));
    function Dt() {
      (this.evEl = ee),
        (this.evWin = ne),
        S.apply(this, arguments),
        (this.store = this.manager.session.pointerEvents = []);
    }
    P(Dt, S, {
      handler: function (e) {
        var n = this.store,
          i = !1,
          r = e.type.toLowerCase().replace("ms", ""),
          s = Xe[r],
          o = Ve[e.pointerType] || e.pointerType,
          d = o == Z,
          p = z(n, e.pointerId, "pointerId");
        s & g && (e.button === 0 || d)
          ? p < 0 && (n.push(e), (p = n.length - 1))
          : s & (h | _) && (i = !0),
          !(p < 0) &&
            ((n[p] = e),
            this.callback(this.manager, s, {
              pointers: n,
              changedPointers: [e],
              pointerType: o,
              srcEvent: e,
            }),
            i && n.splice(p, 1));
      },
    });
    var ze = { touchstart: g, touchmove: Y, touchend: h, touchcancel: _ },
      qe = "touchstart",
      Be = "touchstart touchmove touchend touchcancel";
    function ie() {
      (this.evTarget = qe),
        (this.evWin = Be),
        (this.started = !1),
        S.apply(this, arguments);
    }
    P(ie, S, {
      handler: function (e) {
        var n = ze[e.type];
        if ((n === g && (this.started = !0), !!this.started)) {
          var i = Ge.call(this, e, n);
          n & (h | _) && i[0].length - i[1].length === 0 && (this.started = !1),
            this.callback(this.manager, n, {
              pointers: i[0],
              changedPointers: i[1],
              pointerType: Z,
              srcEvent: e,
            });
        }
      },
    });
    function Ge(t, e) {
      var n = ut(t.touches),
        i = ut(t.changedTouches);
      return e & (h | _) && (n = Bt(n.concat(i), "identifier", !0)), [n, i];
    }
    var $e = { touchstart: g, touchmove: Y, touchend: h, touchcancel: _ },
      Ze = "touchstart touchmove touchend touchcancel";
    function Tt() {
      (this.evTarget = Ze), (this.targetIds = {}), S.apply(this, arguments);
    }
    P(Tt, S, {
      handler: function (e) {
        var n = $e[e.type],
          i = je.call(this, e, n);
        i &&
          this.callback(this.manager, n, {
            pointers: i[0],
            changedPointers: i[1],
            pointerType: Z,
            srcEvent: e,
          });
      },
    });
    function je(t, e) {
      var n = ut(t.touches),
        i = this.targetIds;
      if (e & (g | Y) && n.length === 1)
        return (i[n[0].identifier] = !0), [n, n];
      var r,
        s,
        o = ut(t.changedTouches),
        d = [],
        p = this.target;
      if (
        ((s = n.filter(function (E) {
          return qt(E.target, p);
        })),
        e === g)
      )
        for (r = 0; r < s.length; ) (i[s[r].identifier] = !0), r++;
      for (r = 0; r < o.length; )
        i[o[r].identifier] && d.push(o[r]),
          e & (h | _) && delete i[o[r].identifier],
          r++;
      if (d.length) return [Bt(s.concat(d), "identifier", !0), d];
    }
    var Je = 2500,
      re = 25;
    function bt() {
      S.apply(this, arguments);
      var t = Ot(this.handler, this);
      (this.touch = new Tt(this.manager, t)),
        (this.mouse = new pt(this.manager, t)),
        (this.primaryTouch = null),
        (this.lastTouches = []);
    }
    P(bt, S, {
      handler: function (e, n, i) {
        var r = i.pointerType == Z,
          s = i.pointerType == Nt;
        if (
          !(s && i.sourceCapabilities && i.sourceCapabilities.firesTouchEvents)
        ) {
          if (r) Qe.call(this, n, i);
          else if (s && Ke.call(this, i)) return;
          this.callback(e, n, i);
        }
      },
      destroy: function () {
        this.touch.destroy(), this.mouse.destroy();
      },
    });
    function Qe(t, e) {
      t & g
        ? ((this.primaryTouch = e.changedPointers[0].identifier),
          se.call(this, e))
        : t & (h | _) && se.call(this, e);
    }
    function se(t) {
      var e = t.changedPointers[0];
      if (e.identifier === this.primaryTouch) {
        var n = { x: e.clientX, y: e.clientY };
        this.lastTouches.push(n);
        var i = this.lastTouches,
          r = function () {
            var s = i.indexOf(n);
            s > -1 && i.splice(s, 1);
          };
        setTimeout(r, Je);
      }
    }
    function Ke(t) {
      for (
        var e = t.srcEvent.clientX, n = t.srcEvent.clientY, i = 0;
        i < this.lastTouches.length;
        i++
      ) {
        var r = this.lastTouches[i],
          s = Math.abs(e - r.x),
          o = Math.abs(n - r.y);
        if (s <= re && o <= re) return !0;
      }
      return !1;
    }
    var ae = ht(U.style, "touchAction"),
      oe = ae !== c,
      le = "compute",
      ce = "auto",
      Mt = "manipulation",
      V = "none",
      tt = "pan-x",
      et = "pan-y",
      mt = en();
    function kt(t, e) {
      (this.manager = t), this.set(e);
    }
    kt.prototype = {
      set: function (t) {
        t == le && (t = this.compute()),
          oe &&
            this.manager.element.style &&
            mt[t] &&
            (this.manager.element.style[ae] = t),
          (this.actions = t.toLowerCase().trim());
      },
      update: function () {
        this.set(this.manager.options.touchAction);
      },
      compute: function () {
        var t = [];
        return (
          C(this.manager.recognizers, function (e) {
            St(e.options.enable, [e]) && (t = t.concat(e.getTouchAction()));
          }),
          tn(t.join(" "))
        );
      },
      preventDefaults: function (t) {
        var e = t.srcEvent,
          n = t.offsetDirection;
        if (this.manager.session.prevented) {
          e.preventDefault();
          return;
        }
        var i = this.actions,
          r = W(i, V) && !mt[V],
          s = W(i, et) && !mt[et],
          o = W(i, tt) && !mt[tt];
        if (r) {
          var d = t.pointers.length === 1,
            p = t.distance < 2,
            E = t.deltaTime < 250;
          if (d && p && E) return;
        }
        if (!(o && s) && (r || (s && n & b) || (o && n & X)))
          return this.preventSrc(e);
      },
      preventSrc: function (t) {
        (this.manager.session.prevented = !0), t.preventDefault();
      },
    };
    function tn(t) {
      if (W(t, V)) return V;
      var e = W(t, tt),
        n = W(t, et);
      return e && n ? V : e || n ? (e ? tt : et) : W(t, Mt) ? Mt : ce;
    }
    function en() {
      if (!oe) return !1;
      var t = {},
        e = l.CSS && l.CSS.supports;
      return (
        [
          "auto",
          "manipulation",
          "pan-y",
          "pan-x",
          "pan-x pan-y",
          "none",
        ].forEach(function (n) {
          t[n] = e ? l.CSS.supports("touch-action", n) : !0;
        }),
        t
      );
    }
    var gt = 1,
      N = 2,
      q = 4,
      L = 8,
      x = L,
      nt = 16,
      M = 32;
    function w(t) {
      (this.options = O({}, this.defaults, t || {})),
        (this.id = Oe()),
        (this.manager = null),
        (this.options.enable = zt(this.options.enable, !0)),
        (this.state = gt),
        (this.simultaneous = {}),
        (this.requireFail = []);
    }
    w.prototype = {
      defaults: {},
      set: function (t) {
        return (
          O(this.options, t),
          this.manager && this.manager.touchAction.update(),
          this
        );
      },
      recognizeWith: function (t) {
        if (T(t, "recognizeWith", this)) return this;
        var e = this.simultaneous;
        return (
          (t = Et(t, this)),
          e[t.id] || ((e[t.id] = t), t.recognizeWith(this)),
          this
        );
      },
      dropRecognizeWith: function (t) {
        return T(t, "dropRecognizeWith", this)
          ? this
          : ((t = Et(t, this)), delete this.simultaneous[t.id], this);
      },
      requireFailure: function (t) {
        if (T(t, "requireFailure", this)) return this;
        var e = this.requireFail;
        return (
          (t = Et(t, this)),
          z(e, t) === -1 && (e.push(t), t.requireFailure(this)),
          this
        );
      },
      dropRequireFailure: function (t) {
        if (T(t, "dropRequireFailure", this)) return this;
        t = Et(t, this);
        var e = z(this.requireFail, t);
        return e > -1 && this.requireFail.splice(e, 1), this;
      },
      hasRequireFailures: function () {
        return this.requireFail.length > 0;
      },
      canRecognizeWith: function (t) {
        return !!this.simultaneous[t.id];
      },
      emit: function (t) {
        var e = this,
          n = this.state;
        function i(r) {
          e.manager.emit(r, t);
        }
        n < L && i(e.options.event + ue(n)),
          i(e.options.event),
          t.additionalEvent && i(t.additionalEvent),
          n >= L && i(e.options.event + ue(n));
      },
      tryEmit: function (t) {
        if (this.canEmit()) return this.emit(t);
        this.state = M;
      },
      canEmit: function () {
        for (var t = 0; t < this.requireFail.length; ) {
          if (!(this.requireFail[t].state & (M | gt))) return !1;
          t++;
        }
        return !0;
      },
      recognize: function (t) {
        var e = O({}, t);
        if (!St(this.options.enable, [this, e])) {
          this.reset(), (this.state = M);
          return;
        }
        this.state & (x | nt | M) && (this.state = gt),
          (this.state = this.process(e)),
          this.state & (N | q | L | nt) && this.tryEmit(e);
      },
      process: function (t) {},
      getTouchAction: function () {},
      reset: function () {},
    };
    function ue(t) {
      return t & nt
        ? "cancel"
        : t & L
        ? "end"
        : t & q
        ? "move"
        : t & N
        ? "start"
        : "";
    }
    function he(t) {
      return t == K
        ? "down"
        : t == Q
        ? "up"
        : t == j
        ? "left"
        : t == J
        ? "right"
        : "";
    }
    function Et(t, e) {
      var n = e.manager;
      return n ? n.get(t) : t;
    }
    function A() {
      w.apply(this, arguments);
    }
    P(A, w, {
      defaults: { pointers: 1 },
      attrTest: function (t) {
        var e = this.options.pointers;
        return e === 0 || t.pointers.length === e;
      },
      process: function (t) {
        var e = this.state,
          n = t.eventType,
          i = e & (N | q),
          r = this.attrTest(t);
        return i && (n & _ || !r)
          ? e | nt
          : i || r
          ? n & h
            ? e | L
            : e & N
            ? e | q
            : N
          : M;
      },
    });
    function yt() {
      A.apply(this, arguments), (this.pX = null), (this.pY = null);
    }
    P(yt, A, {
      defaults: { event: "pan", threshold: 10, pointers: 1, direction: Zt },
      getTouchAction: function () {
        var t = this.options.direction,
          e = [];
        return t & b && e.push(et), t & X && e.push(tt), e;
      },
      directionTest: function (t) {
        var e = this.options,
          n = !0,
          i = t.distance,
          r = t.direction,
          s = t.deltaX,
          o = t.deltaY;
        return (
          r & e.direction ||
            (e.direction & b
              ? ((r = s === 0 ? ft : s < 0 ? j : J),
                (n = s != this.pX),
                (i = Math.abs(t.deltaX)))
              : ((r = o === 0 ? ft : o < 0 ? Q : K),
                (n = o != this.pY),
                (i = Math.abs(t.deltaY)))),
          (t.direction = r),
          n && i > e.threshold && r & e.direction
        );
      },
      attrTest: function (t) {
        return (
          A.prototype.attrTest.call(this, t) &&
          (this.state & N || (!(this.state & N) && this.directionTest(t)))
        );
      },
      emit: function (t) {
        (this.pX = t.deltaX), (this.pY = t.deltaY);
        var e = he(t.direction);
        e && (t.additionalEvent = this.options.event + e),
          this._super.emit.call(this, t);
      },
    });
    function Ut() {
      A.apply(this, arguments);
    }
    P(Ut, A, {
      defaults: { event: "pinch", threshold: 0, pointers: 2 },
      getTouchAction: function () {
        return [V];
      },
      attrTest: function (t) {
        return (
          this._super.attrTest.call(this, t) &&
          (Math.abs(t.scale - 1) > this.options.threshold || this.state & N)
        );
      },
      emit: function (t) {
        if (t.scale !== 1) {
          var e = t.scale < 1 ? "in" : "out";
          t.additionalEvent = this.options.event + e;
        }
        this._super.emit.call(this, t);
      },
    });
    function xt() {
      w.apply(this, arguments), (this._timer = null), (this._input = null);
    }
    P(xt, w, {
      defaults: { event: "press", pointers: 1, time: 251, threshold: 9 },
      getTouchAction: function () {
        return [ce];
      },
      process: function (t) {
        var e = this.options,
          n = t.pointers.length === e.pointers,
          i = t.distance < e.threshold,
          r = t.deltaTime > e.time;
        if (((this._input = t), !i || !n || (t.eventType & (h | _) && !r)))
          this.reset();
        else if (t.eventType & g)
          this.reset(),
            (this._timer = I(
              function () {
                (this.state = x), this.tryEmit();
              },
              e.time,
              this
            ));
        else if (t.eventType & h) return x;
        return M;
      },
      reset: function () {
        clearTimeout(this._timer);
      },
      emit: function (t) {
        this.state === x &&
          (t && t.eventType & h
            ? this.manager.emit(this.options.event + "up", t)
            : ((this._input.timeStamp = $()),
              this.manager.emit(this.options.event, this._input)));
      },
    });
    function wt() {
      A.apply(this, arguments);
    }
    P(wt, A, {
      defaults: { event: "rotate", threshold: 0, pointers: 2 },
      getTouchAction: function () {
        return [V];
      },
      attrTest: function (t) {
        return (
          this._super.attrTest.call(this, t) &&
          (Math.abs(t.rotation) > this.options.threshold || this.state & N)
        );
      },
    });
    function Rt() {
      A.apply(this, arguments);
    }
    P(Rt, A, {
      defaults: {
        event: "swipe",
        threshold: 10,
        velocity: 0.3,
        direction: b | X,
        pointers: 1,
      },
      getTouchAction: function () {
        return yt.prototype.getTouchAction.call(this);
      },
      attrTest: function (t) {
        var e = this.options.direction,
          n;
        return (
          e & (b | X)
            ? (n = t.overallVelocity)
            : e & b
            ? (n = t.overallVelocityX)
            : e & X && (n = t.overallVelocityY),
          this._super.attrTest.call(this, t) &&
            e & t.offsetDirection &&
            t.distance > this.options.threshold &&
            t.maxPointers == this.options.pointers &&
            u(n) > this.options.velocity &&
            t.eventType & h
        );
      },
      emit: function (t) {
        var e = he(t.offsetDirection);
        e && this.manager.emit(this.options.event + e, t),
          this.manager.emit(this.options.event, t);
      },
    });
    function _t() {
      w.apply(this, arguments),
        (this.pTime = !1),
        (this.pCenter = !1),
        (this._timer = null),
        (this._input = null),
        (this.count = 0);
    }
    P(_t, w, {
      defaults: {
        event: "tap",
        pointers: 1,
        taps: 1,
        interval: 300,
        time: 250,
        threshold: 9,
        posThreshold: 10,
      },
      getTouchAction: function () {
        return [Mt];
      },
      process: function (t) {
        var e = this.options,
          n = t.pointers.length === e.pointers,
          i = t.distance < e.threshold,
          r = t.deltaTime < e.time;
        if ((this.reset(), t.eventType & g && this.count === 0))
          return this.failTimeout();
        if (i && r && n) {
          if (t.eventType != h) return this.failTimeout();
          var s = this.pTime ? t.timeStamp - this.pTime < e.interval : !0,
            o = !this.pCenter || dt(this.pCenter, t.center) < e.posThreshold;
          (this.pTime = t.timeStamp),
            (this.pCenter = t.center),
            !o || !s ? (this.count = 1) : (this.count += 1),
            (this._input = t);
          var d = this.count % e.taps;
          if (d === 0)
            return this.hasRequireFailures()
              ? ((this._timer = I(
                  function () {
                    (this.state = x), this.tryEmit();
                  },
                  e.interval,
                  this
                )),
                N)
              : x;
        }
        return M;
      },
      failTimeout: function () {
        return (
          (this._timer = I(
            function () {
              this.state = M;
            },
            this.options.interval,
            this
          )),
          M
        );
      },
      reset: function () {
        clearTimeout(this._timer);
      },
      emit: function () {
        this.state == x &&
          ((this._input.tapCount = this.count),
          this.manager.emit(this.options.event, this._input));
      },
    });
    function R(t, e) {
      return (
        (e = e || {}),
        (e.recognizers = zt(e.recognizers, R.defaults.preset)),
        new Lt(t, e)
      );
    }
    (R.VERSION = "2.0.7"),
      (R.defaults = {
        domEvents: !1,
        touchAction: le,
        enable: !0,
        inputTarget: null,
        inputClass: null,
        preset: [
          [wt, { enable: !1 }],
          [Ut, { enable: !1 }, ["rotate"]],
          [Rt, { direction: b }],
          [yt, { direction: b }, ["swipe"]],
          [_t],
          [_t, { event: "doubletap", taps: 2 }, ["tap"]],
          [xt],
        ],
        cssProps: {
          userSelect: "none",
          touchSelect: "none",
          touchCallout: "none",
          contentZooming: "none",
          userDrag: "none",
          tapHighlightColor: "rgba(0,0,0,0)",
        },
      });
    var nn = 1,
      fe = 2;
    function Lt(t, e) {
      (this.options = O({}, R.defaults, e || {})),
        (this.options.inputTarget = this.options.inputTarget || t),
        (this.handlers = {}),
        (this.session = {}),
        (this.recognizers = []),
        (this.oldCssProps = {}),
        (this.element = t),
        (this.input = ke(this)),
        (this.touchAction = new kt(this, this.options.touchAction)),
        ve(this, !0),
        C(
          this.options.recognizers,
          function (n) {
            var i = this.add(new n[0](n[1]));
            n[2] && i.recognizeWith(n[2]), n[3] && i.requireFailure(n[3]);
          },
          this
        );
    }
    Lt.prototype = {
      set: function (t) {
        return (
          O(this.options, t),
          t.touchAction && this.touchAction.update(),
          t.inputTarget &&
            (this.input.destroy(),
            (this.input.target = t.inputTarget),
            this.input.init()),
          this
        );
      },
      stop: function (t) {
        this.session.stopped = t ? fe : nn;
      },
      recognize: function (t) {
        var e = this.session;
        if (!e.stopped) {
          this.touchAction.preventDefaults(t);
          var n,
            i = this.recognizers,
            r = e.curRecognizer;
          (!r || (r && r.state & x)) && (r = e.curRecognizer = null);
          for (var s = 0; s < i.length; )
            (n = i[s]),
              e.stopped !== fe && (!r || n == r || n.canRecognizeWith(r))
                ? n.recognize(t)
                : n.reset(),
              !r && n.state & (N | q | L) && (r = e.curRecognizer = n),
              s++;
        }
      },
      get: function (t) {
        if (t instanceof w) return t;
        for (var e = this.recognizers, n = 0; n < e.length; n++)
          if (e[n].options.event == t) return e[n];
        return null;
      },
      add: function (t) {
        if (T(t, "add", this)) return this;
        var e = this.get(t.options.event);
        return (
          e && this.remove(e),
          this.recognizers.push(t),
          (t.manager = this),
          this.touchAction.update(),
          t
        );
      },
      remove: function (t) {
        if (T(t, "remove", this)) return this;
        if (((t = this.get(t)), t)) {
          var e = this.recognizers,
            n = z(e, t);
          n !== -1 && (e.splice(n, 1), this.touchAction.update());
        }
        return this;
      },
      on: function (t, e) {
        if (t !== c && e !== c) {
          var n = this.handlers;
          return (
            C(ct(t), function (i) {
              (n[i] = n[i] || []), n[i].push(e);
            }),
            this
          );
        }
      },
      off: function (t, e) {
        if (t !== c) {
          var n = this.handlers;
          return (
            C(ct(t), function (i) {
              e ? n[i] && n[i].splice(z(n[i], e), 1) : delete n[i];
            }),
            this
          );
        }
      },
      emit: function (t, e) {
        this.options.domEvents && rn(t, e);
        var n = this.handlers[t] && this.handlers[t].slice();
        if (!(!n || !n.length)) {
          (e.type = t),
            (e.preventDefault = function () {
              e.srcEvent.preventDefault();
            });
          for (var i = 0; i < n.length; ) n[i](e), i++;
        }
      },
      destroy: function () {
        this.element && ve(this, !1),
          (this.handlers = {}),
          (this.session = {}),
          this.input.destroy(),
          (this.element = null);
      },
    };
    function ve(t, e) {
      var n = t.element;
      if (n.style) {
        var i;
        C(t.options.cssProps, function (r, s) {
          (i = ht(n.style, s)),
            e
              ? ((t.oldCssProps[i] = n.style[i]), (n.style[i] = r))
              : (n.style[i] = t.oldCssProps[i] || "");
        }),
          e || (t.oldCssProps = {});
      }
    }
    function rn(t, e) {
      var n = y.createEvent("Event");
      n.initEvent(t, !0, !0), (n.gesture = e), e.target.dispatchEvent(n);
    }
    O(R, {
      INPUT_START: g,
      INPUT_MOVE: Y,
      INPUT_END: h,
      INPUT_CANCEL: _,
      STATE_POSSIBLE: gt,
      STATE_BEGAN: N,
      STATE_CHANGED: q,
      STATE_ENDED: L,
      STATE_RECOGNIZED: x,
      STATE_CANCELLED: nt,
      STATE_FAILED: M,
      DIRECTION_NONE: ft,
      DIRECTION_LEFT: j,
      DIRECTION_RIGHT: J,
      DIRECTION_UP: Q,
      DIRECTION_DOWN: K,
      DIRECTION_HORIZONTAL: b,
      DIRECTION_VERTICAL: X,
      DIRECTION_ALL: Zt,
      Manager: Lt,
      Input: S,
      TouchAction: kt,
      TouchInput: Tt,
      MouseInput: pt,
      PointerEventInput: Dt,
      TouchMouseInput: bt,
      SingleTouchInput: ie,
      Recognizer: w,
      AttrRecognizer: A,
      Tap: _t,
      Pan: yt,
      Swipe: Rt,
      Pinch: Ut,
      Rotate: wt,
      Press: xt,
      on: ot,
      off: lt,
      each: C,
      merge: Pe,
      extend: Ct,
      assign: O,
      inherit: P,
      bindFn: Ot,
      prefixed: ht,
    });
    var sn =
      typeof l != "undefined" ? l : typeof self != "undefined" ? self : {};
    (sn.Hammer = R),
      typeof c == "function" && c.amd
        ? c(function () {
            return R;
          })
        : a.exports
        ? (a.exports = R)
        : (l[f] = R);
  })(window, document, "Hammer");
})(Ie);
var si = Ie.exports;
const fi = In(si),
  ai = { class: "pl-5 py-2.5 text-sm overflow-scroll bg-white" },
  oi = { class: "whitespace-nowrap h-7" },
  li = ["onClick"],
  vi = G({
    __name: "BarPrice",
    props: { data: {}, value: {} },
    emits: ["tapItem"],
    setup(a) {
      return (l, y) => (
        Ft(),
        Ht("div", ai, [
          Wt("div", oi, [
            (Ft(!0),
            Ht(
              _e,
              null,
              Pn(
                l.data,
                (f) => (
                  Ft(),
                  Ht(
                    "span",
                    {
                      key: f.code,
                      onClick: (c) =>
                        f.isForbiden || l.$emit("tapItem", f.code),
                      class: Cn([
                        "inline-flex items-center px-2 mr-2 leading-7 border rounded-3xl",
                        [
                          f.code === l.value && !f.isForbiden
                            ? "border-black"
                            : "border-lighter-gray",
                          f.isForbiden ? "text-gray-200" : "text-gray/700",
                        ],
                      ]),
                    },
                    [
                      Wt(
                        "i",
                        {
                          class: "w-3 h-3 mr-1 rounded-full",
                          style: On({ backgroundColor: f.color }),
                        },
                        null,
                        4
                      ),
                      Wt(
                        "span",
                        null,
                        Te(f.verboseName) + " " + Te(f.price) + "元",
                        1
                      ),
                    ],
                    10,
                    li
                  )
                )
              ),
              128
            )),
          ]),
        ])
      );
    },
  });
export { fi as H, hi as S, vi as _, ui as s };
